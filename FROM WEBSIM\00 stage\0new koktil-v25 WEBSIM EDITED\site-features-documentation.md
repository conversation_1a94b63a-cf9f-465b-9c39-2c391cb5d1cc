# ميزات فتح المواقع داخل التطبيق

## الميزات الجديدة المضافة:

### 1. **فتح المواقع داخل التطبيق** 🌐
- ✅ إمكانية فتح المواقع من مجلدات مواقع الأفلام داخل التطبيق
- ✅ إمكانية فتح المواقع من القائمة الرئيسية داخل التطبيق
- ✅ عرض المواقع في iframe مع إمكانية التنقل الكامل
- ✅ تحديث عنوان الصفحة ديناميكياً عند فتح المواقع

### 2. **إضافة المواقع كأفلام** 🎬
- ✅ زر "إضافة كفيلم" في مودال تشغيل المواقع
- ✅ مودال مخصص لإدخال تفاصيل الفيلم/المسلسل
- ✅ اختيار القسم المناسب للفيلم الجديد
- ✅ إمكانية تحديد نوع المحتوى (فيلم أو مسلسل)
- ✅ إضافة رابط صورة اختياري

### 3. **تحسينات واجهة المستخدم** 🎨
- ✅ أزرار منفصلة للفتح الداخلي والخارجي
- ✅ أيقونات واضحة ومميزة لكل نوع فتح
- ✅ تأثيرات بصرية جذابة للأزرار
- ✅ تصميم متجاوب يعمل على جميع الشاشات

## كيفية الاستخدام:

### فتح المواقع من المجلدات:
1. اذهب إلى قسم "مواقع الأفلام"
2. افتح أي مجلد يحتوي على مواقع
3. ستجد زرين لكل موقع:
   - **زر أزرق (فتح داخلياً)**: يفتح الموقع داخل التطبيق
   - **زر أخضر (فتح خارجياً)**: يفتح الموقع في نافذة جديدة

### فتح المواقع من القائمة الرئيسية:
1. اذهب إلى قسم "مواقع الأفلام"
2. في القائمة الرئيسية ستجد زرين لكل موقع:
   - **أيقونة الشاشة**: فتح داخلي
   - **أيقونة الرابط الخارجي**: فتح خارجي

### إضافة موقع كفيلم:
1. افتح أي موقع داخل التطبيق
2. انقر على زر "إضافة كفيلم" (البنفسجي) في أعلى المودال
3. املأ التفاصيل المطلوبة:
   - اسم الفيلم/المسلسل
   - رابط المحتوى
   - رابط الصورة (اختياري)
   - القسم المناسب
   - نوع المحتوى (فيلم أو مسلسل)
4. انقر "حفظ كفيلم"

### إضافة الموقع إلى قسم فرعي:
1. أثناء تشغيل الموقع داخل التطبيق
2. انقر على زر "إضافة إلى قسم" (البرتقالي)
3. اختر القسم الفرعي المطلوب

## الميزات التقنية:

### تتبع عنوان الصفحة:
- العنوان يتغير إلى "تصفح: [اسم الموقع]" عند فتح المواقع
- محاولة تتبع تغيير العنوان داخل iframe (محدود بسبب CORS)
- استعادة العنوان الأصلي عند الإغلاق

### إدارة البيانات:
- الأفلام المضافة من المواقع تُحفظ في قاعدة البيانات المحلية
- تحديث تلقائي لعدادات الأقسام
- دعم كامل لنظام الأقسام الفرعية

### الأمان والأداء:
- استخدام iframe آمن لعرض المواقع
- تنظيف البيانات المدخلة لمنع XSS
- إدارة ذاكرة فعالة للمودالات

## الملفات المعدلة:

### `app.js`:
- إضافة وظيفة `openSiteInApp()` لفتح المواقع داخلياً
- إضافة وظيفة `openSitePlayModal()` لمودال تشغيل المواقع
- إضافة وظيفة `openAddAsMovieModal()` لإضافة المواقع كأفلام
- تحديث مستمعي الأحداث للأزرار الجديدة
- تحسين وظيفة `showFolderContents()` لدعم الفتح الداخلي

### `styles.css`:
- أنماط جديدة للأزرار الداخلية والخارجية
- تحسين تصميم مودال إضافة الأفلام
- تأثيرات بصرية للأزرار الجديدة
- دعم التصميم المتجاوب

## فوائد الميزات الجديدة:

1. **تجربة مستخدم محسنة**: فتح المواقع داخل التطبيق يوفر تجربة سلسة
2. **إدارة أفضل للمحتوى**: إمكانية تحويل المواقع إلى أفلام منظمة
3. **مرونة في الاستخدام**: خيارات متعددة للفتح (داخلي/خارجي)
4. **تنظيم محسن**: ربط المواقع بنظام الأقسام والأقسام الفرعية

## التحسينات الجديدة لتتبع العنوان: 🔄

### 4. **تتبع ديناميكي لعنوان الصفحة** 📝
- ✅ تحديث عنوان الصفحة عند التنقل داخل iframe
- ✅ استخراج عنوان ذكي من URL الجديد
- ✅ تتبع تغيير URL باستخدام عدة طرق:
  - MutationObserver لتتبع تغيير src
  - PostMessage للتواصل مع iframe
  - تتبع أحداث التنقل (hashchange, beforeunload)
  - تتبع النقر داخل iframe
- ✅ وظيفة `extractTitleFromUrl()` لاستخراج عنوان أفضل

### كيفية عمل تتبع العنوان:

#### 1. **استخراج العنوان من URL**:
```javascript
// مثال على استخراج العنوان
https://example.com/movies/action/batman.html
↓
"Batman - Example"

https://youtube.com/watch?v=abc123&title=My+Video
↓
"My Video"

https://netflix.com/browse/genre/action
↓
"Action - Netflix"
```

#### 2. **طرق التتبع المتعددة**:
- **تتبع URL**: كل ثانيتين للتحقق من تغيير الرابط
- **تتبع العنوان**: كل 3 ثوانٍ لمحاولة قراءة عنوان الصفحة
- **MutationObserver**: تتبع فوري لتغيير src
- **PostMessage**: تواصل مع iframe إذا كان ممكناً
- **أحداث التنقل**: تتبع hashchange و beforeunload

#### 3. **استخراج ذكي للعنوان**:
- البحث في معاملات URL عن: title, q, search, query, name
- تنظيف أسماء الملفات من المسار
- إزالة امتدادات الملفات (.html, .php, إلخ)
- تحويل الشرطات والشرطات السفلية إلى مسافات
- تكبير الحروف الأولى

### مثال على التحسينات:

**قبل التحسين**:
```
عنوان ثابت: "تصفح: موقع الأفلام - New Koktil-aflam v25"
```

**بعد التحسين**:
```
عند فتح الموقع: "موقع الأفلام - New Koktil-aflam v25"
عند التنقل لقسم الأكشن: "Action Movies - موقع الأفلام - New Koktil-aflam v25"
عند فتح فيلم معين: "Batman Movie - موقع الأفلام - New Koktil-aflam v25"
```

## التحسينات الأخيرة: 🚀

### 5. **حل مشكلة تتبع العنوان** 🔧
- ✅ **تتبع مكثف**: فحص كل 800ms بدلاً من كل ثانيتين
- ✅ **تتبع أحداث الماوس**: تحديث عند النقر والتمرير
- ✅ **متغيرات عامة**: `window.currentIframeUrl` و `window.currentIframeTitle`
- ✅ **زر التحديث اليدوي**: 🔄 لتحديث العنوان يدوياً
- ✅ **رسائل توضيحية**: نصائح للمستخدم

### 6. **تحسين "إضافة كفيلم"** 🎬
- ✅ **URL الحالي**: يستخدم الرابط الحالي للصفحة المفتوحة
- ✅ **تظليل النص**: اسم الفيلم يُظلل تلقائياً للتعديل
- ✅ **عنوان ديناميكي**: يستخدم العنوان الحالي للصفحة
- ✅ **تحديث فوري**: البيانات تتحدث مع كل تنقل

### كيفية استخدام الميزات الجديدة:

#### تتبع العنوان المحسن:
1. **تلقائي**: العنوان يتحدث تلقائياً (قد يكون محدود)
2. **يدوي**: استخدم زر 🔄 لتحديث العنوان يدوياً
3. **أحداث الماوس**: انقر داخل iframe لتحفيز التحديث

#### إضافة كفيلم محسنة:
1. تصفح داخل الموقع للصفحة المطلوبة
2. انقر زر "إضافة كفيلم"
3. اسم الفيلم سيكون مظلل - عدله حسب الحاجة
4. رابط الفيلم سيكون الصفحة الحالية
5. احفظ الفيلم

### مثال عملي:
```
1. فتح موقع Netflix داخلياً
2. التنقل لفيلم معين: netflix.com/watch/12345
3. العنوان يصبح: "Movie Title - Netflix"
4. النقر على "إضافة كفيلم":
   - الاسم: "Movie Title - Netflix" (مظلل للتعديل)
   - الرابط: "netflix.com/watch/12345" (الصفحة الحالية)
```

## ملاحظات مهمة:

### قيود CORS والحلول:
- **المشكلة**: معظم المواقع تمنع الوصول لمحتوى iframe لأسباب أمنية
- **الحل**: تتبع مكثف + زر التحديث اليدوي + أحداث الماوس
- **نصيحة**: استخدم زر 🔄 إذا لم يتحدث العنوان تلقائياً

### أفضل الممارسات:
- انقر داخل iframe بعد التنقل لتحفيز التحديث
- استخدم زر التحديث اليدوي عند الحاجة
- العنوان قد يتأخر قليلاً بسبب قيود الأمان
- جميع البيانات تُحفظ محلياً في المتصفح
- يُنصح بتجربة الفتح الخارجي إذا لم يعمل الفتح الداخلي
