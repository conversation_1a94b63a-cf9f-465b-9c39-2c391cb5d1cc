# ميزات فتح المواقع داخل التطبيق

## الميزات الجديدة المضافة:

### 1. **فتح المواقع داخل التطبيق** 🌐
- ✅ إمكانية فتح المواقع من مجلدات مواقع الأفلام داخل التطبيق
- ✅ إمكانية فتح المواقع من القائمة الرئيسية داخل التطبيق
- ✅ عرض المواقع في iframe مع إمكانية التنقل الكامل
- ✅ تحديث عنوان الصفحة ديناميكياً عند فتح المواقع

### 2. **إضافة المواقع كأفلام** 🎬
- ✅ زر "إضافة كفيلم" في مودال تشغيل المواقع
- ✅ مودال مخصص لإدخال تفاصيل الفيلم/المسلسل
- ✅ اختيار القسم المناسب للفيلم الجديد
- ✅ إمكانية تحديد نوع المحتوى (فيلم أو مسلسل)
- ✅ إضافة رابط صورة اختياري

### 3. **تحسينات واجهة المستخدم** 🎨
- ✅ أزرار منفصلة للفتح الداخلي والخارجي
- ✅ أيقونات واضحة ومميزة لكل نوع فتح
- ✅ تأثيرات بصرية جذابة للأزرار
- ✅ تصميم متجاوب يعمل على جميع الشاشات

## كيفية الاستخدام:

### فتح المواقع من المجلدات:
1. اذهب إلى قسم "مواقع الأفلام"
2. افتح أي مجلد يحتوي على مواقع
3. ستجد زرين لكل موقع:
   - **زر أزرق (فتح داخلياً)**: يفتح الموقع داخل التطبيق
   - **زر أخضر (فتح خارجياً)**: يفتح الموقع في نافذة جديدة

### فتح المواقع من القائمة الرئيسية:
1. اذهب إلى قسم "مواقع الأفلام"
2. في القائمة الرئيسية ستجد زرين لكل موقع:
   - **أيقونة الشاشة**: فتح داخلي
   - **أيقونة الرابط الخارجي**: فتح خارجي

### إضافة موقع كفيلم:
1. افتح أي موقع داخل التطبيق
2. انقر على زر "إضافة كفيلم" (البنفسجي) في أعلى المودال
3. املأ التفاصيل المطلوبة:
   - اسم الفيلم/المسلسل
   - رابط المحتوى
   - رابط الصورة (اختياري)
   - القسم المناسب
   - نوع المحتوى (فيلم أو مسلسل)
4. انقر "حفظ كفيلم"

### إضافة الموقع إلى قسم فرعي:
1. أثناء تشغيل الموقع داخل التطبيق
2. انقر على زر "إضافة إلى قسم" (البرتقالي)
3. اختر القسم الفرعي المطلوب

## الميزات التقنية:

### تتبع عنوان الصفحة:
- العنوان يتغير إلى "تصفح: [اسم الموقع]" عند فتح المواقع
- محاولة تتبع تغيير العنوان داخل iframe (محدود بسبب CORS)
- استعادة العنوان الأصلي عند الإغلاق

### إدارة البيانات:
- الأفلام المضافة من المواقع تُحفظ في قاعدة البيانات المحلية
- تحديث تلقائي لعدادات الأقسام
- دعم كامل لنظام الأقسام الفرعية

### الأمان والأداء:
- استخدام iframe آمن لعرض المواقع
- تنظيف البيانات المدخلة لمنع XSS
- إدارة ذاكرة فعالة للمودالات

## الملفات المعدلة:

### `app.js`:
- إضافة وظيفة `openSiteInApp()` لفتح المواقع داخلياً
- إضافة وظيفة `openSitePlayModal()` لمودال تشغيل المواقع
- إضافة وظيفة `openAddAsMovieModal()` لإضافة المواقع كأفلام
- تحديث مستمعي الأحداث للأزرار الجديدة
- تحسين وظيفة `showFolderContents()` لدعم الفتح الداخلي

### `styles.css`:
- أنماط جديدة للأزرار الداخلية والخارجية
- تحسين تصميم مودال إضافة الأفلام
- تأثيرات بصرية للأزرار الجديدة
- دعم التصميم المتجاوب

## فوائد الميزات الجديدة:

1. **تجربة مستخدم محسنة**: فتح المواقع داخل التطبيق يوفر تجربة سلسة
2. **إدارة أفضل للمحتوى**: إمكانية تحويل المواقع إلى أفلام منظمة
3. **مرونة في الاستخدام**: خيارات متعددة للفتح (داخلي/خارجي)
4. **تنظيم محسن**: ربط المواقع بنظام الأقسام والأقسام الفرعية

## ملاحظات مهمة:

- بعض المواقع قد تمنع العرض في iframe لأسباب أمنية
- تتبع عنوان الصفحة داخل iframe محدود بسبب قيود CORS
- يُنصح بتجربة الفتح الخارجي إذا لم يعمل الفتح الداخلي
- جميع البيانات المضافة تُحفظ محلياً في المتصفح
