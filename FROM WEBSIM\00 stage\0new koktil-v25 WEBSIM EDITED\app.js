// وظائف النسخ واللصق
function initializeClipboardFunctions() {
    // نسخ النص المحدد من بطاقات الأفلام
    document.addEventListener('mouseup', function() {
        let selectedText = window.getSelection().toString().trim();
        if (selectedText && selectedText.length > 0) {
            let selection = window.getSelection();
            let parent = selection.anchorNode.parentElement;
            if (parent.closest('.movie-card')) {
                navigator.clipboard.writeText(selectedText).then(() => {
                    showNotification('تم نسخ النص بنجاح');
                }).catch(err => {
                    console.error('فشل نسخ النص: ', err);
                });
            }
        }
    });

    // لصق النص في حقل البحث
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('click', function() {
        navigator.clipboard.readText().then(text => {
            if (text) {
                this.value = text;
                const searchEvent = new Event('input');
                this.dispatchEvent(searchEvent);
            }
        }).catch(err => {
            console.error('فشل قراءة الحافظة: ', err);
        });
    });
}

// إضافة دالة إظهار الإشعارات
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.remove();
    }, 2000);
}

// استدعاء الوظائف عند تحميل الصفحة
window.addEventListener('load', initializeClipboardFunctions);

// تهيئة التخزين المحلي
const STORAGE_KEYS = {
    MOVIES: 'movies_data',
    SERIES: 'series_data',
    CATEGORIES: 'categories_data',
    SETTINGS: 'app_settings',
    PASSWORD: 'app_password',
    IMAGES: 'cached_images',
    DEFAULT_IMPORT_SPEED: 'movies_app_default_import_speed'
};

// إعدادات سرعة الاستيراد
const IMPORT_SPEED_SETTINGS = {
    slow: {
        batchSize: 50,
        timeout: 50,
        analyzeDelay: 100
    },
    medium: {
        batchSize: 100,
        timeout: 20,
        analyzeDelay: 50
    },
    fast: {
        batchSize: 200,
        timeout: 5,
        analyzeDelay: 10
    }
};

// تكوين القسم
const DEFAULT_CATEGORIES = {
    main: [
        { id: 'all', name: 'جميع الأفلام والمسلسلات', count: 0 },
        { id: 'old-arabic', name: 'أفلام عربية قديمة', count: 0 },
        { id: 'new-arabic', name: 'أفلام عربية جديدة', count: 0 },
        { id: 'series', name: 'المسلسلات', count: 0 },
        { id: 'foreign1', name: 'أفلام أجنبية 1', count: 0 },
        { id: 'foreign2', name: 'أفلام أجنبية 2', count: 0 },
        { id: 'foreign3', name: 'أفلام أجنبية 3', count: 0 },
        { id: 'horror', name: 'أفلام الرعب', count: 0 },
        { id: 'series-movies', name: 'سلاسل الأفلام', count: 0 },
        { id: 'stars', name: 'أفلام النجوم', count: 0 },
        { id: 'family', name: 'أفلام عائلية', count: 0 },
        { id: 'movie-sites', name: 'مواقع الأفلام', count: 0, isSites: true, shortcuts: [], folders: [] },
        { id: 'pages-section', name: 'قسم الصفحات', count: 0, isPagesSection: true, pages: [] }
    ],
    sub: [
        { id: 'selected1', name: 'أفلام مختارة 1', count: 0 },
        { id: 'selected2', name: 'أفلام مختارة 2', count: 0 },
        { id: 'favorite1', name: 'مفضلة أفلام 1', count: 0 },
        { id: 'favorite2', name: 'مفضلة أفلام 2', count: 0 }
    ],
    special: [
        { id: 'r1', name: 'قسم الأفلام R1', count: 0 },
        { id: 'r2', name: 'قسم الأفلام R2', count: 0 },
        { id: 's1', name: 'قسم الأفلام S1', count: 0 },
        { id: 's2', name: 'قسم الأفلام S2', count: 0 },
        { id: 's3', name: 'قسم الاكس S3', count: 0 },
        { id: 's-sites', name: 'قسم S SITES', count: 0 }
    ],
    specialSub: [
        { id: 'selected-rs1', name: 'أفلام مختارة R+S1', count: 0 },
        { id: 'selected-rs2', name: 'أفلام مختارة R+S2', count: 0 }
    ]
};

// حالة التطبيق
const appState = {
    movies: [],
    series: [],
    categories: JSON.parse(JSON.stringify(DEFAULT_CATEGORIES)),
    currentCategory: 'all',
    currentPage: 1,
    itemsPerPage: 50,
    viewMode: 'grid',
    sortBy: 'name',
    selectedSite: '',
    selectedStar: '',
    showSpecialSections: false,
    password: '5555',
    searchResults: [],
    cachedImages: {},
    inEditMode: false,
    openMoviesExternally: true,
    zoomLevel: 1, // إضافة مستوى التكبير
    currentPageTitle: 'New Koktil-aflam v25' // لحفظ عنوان الصفحة الحالي
};

// تهيئة localForage لتخزين البيانات
localforage.config({
    name: 'MoviesApp',
    version: 1.0,
    storeName: 'movies_app_store',
    description: 'تخزين بيانات تطبيق الأفلام والمسلسلات'
});

// تحميل البيانات من التخزين المحلي
async function loadAppData() {
    try {
        // تحميل كلمة المرور أولاً
        const savedPassword = await localforage.getItem(STORAGE_KEYS.PASSWORD);
        if (savedPassword) {
            appState.password = savedPassword;
        } else {
            await localforage.setItem(STORAGE_KEYS.PASSWORD, appState.password);
        }

        // تحميل الإعدادات
        const settings = await localforage.getItem(STORAGE_KEYS.SETTINGS);
        if (settings) {
            appState.showSpecialSections = settings.showSpecialSections;
            appState.viewMode = settings.viewMode || 'grid';
            appState.sortBy = settings.sortBy || 'name';
            appState.selectedSite = settings.selectedSite || '';
            appState.selectedStar = settings.selectedStar || '';
            appState.itemsPerPage = settings.itemsPerPage || 50;
            appState.openMoviesExternally = settings.openMoviesExternally || false;
            appState.zoomLevel = settings.zoomLevel || 1; // تحميل مستوى التكبير المحفوظ
        }

        // تحميل الأقسام
        const savedCategories = await localforage.getItem(STORAGE_KEYS.CATEGORIES);
        if (savedCategories) {
            appState.categories = savedCategories;

            // التأكد من وجود قسم مواقع الأفلام مع الخصائص المطلوبة
            const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
            if (movieSitesCat) {
                // التأكد من وجود خاصية shortcuts
                if (!movieSitesCat.shortcuts) {
                    movieSitesCat.shortcuts = [];
                }
                // التأكد من وجود خاصية isSites
                if (!movieSitesCat.isSites) {
                    movieSitesCat.isSites = true;
                }
                // التأكد من وجود خاصية folders
                if (!movieSitesCat.folders) {
                    movieSitesCat.folders = [];
                }
            } else {
                // إضافة قسم مواقع الأفلام إذا لم يكن موجوداً
                appState.categories.main.push({
                    id: 'movie-sites',
                    name: 'مواقع الأفلام',
                    count: 0,
                    isSites: true,
                    shortcuts: [],
                    folders: []
                });
            }
        }

        // تحميل الأفلام
        const savedMovies = await localforage.getItem(STORAGE_KEYS.MOVIES);
        if (savedMovies) {
            appState.movies = savedMovies;
        }

        // تحميل المسلسلات
        const savedSeries = await localforage.getItem(STORAGE_KEYS.SERIES);
        if (savedSeries) {
            appState.series = savedSeries;
        }

        // تحميل الصور المخبأة
        const cachedImages = await localforage.getItem(STORAGE_KEYS.IMAGES);
        if (cachedImages) {
            appState.cachedImages = cachedImages;
        }

        // تحديث عدادات الأقسام
        updateCategoriesCounts();
        renderCategories();

        // عرض الأفلام الافتراضية
        displayMovies('all', 1); // هنا نريد الانتقال للأعلى عند تحميل التطبيق

        // إخفاء/إظهار الأقسام الخاصة بناءً على الإعدادات
        toggleSpecialSectionsVisibility();

        // تطبيق مستوى التكبير المحفوظ
        applyZoom();

        console.log('تم تحميل بيانات التطبيق بنجاح');
    } catch (error) {
        console.error('خطأ أثناء تحميل بيانات التطبيق:', error);
    }
}

// حفظ بيانات التطبيق
async function saveAppData() {
    try {
        await localforage.setItem(STORAGE_KEYS.MOVIES, appState.movies);
        await localforage.setItem(STORAGE_KEYS.SERIES, appState.series);
        await localforage.setItem(STORAGE_KEYS.CATEGORIES, appState.categories);
        await localforage.setItem(STORAGE_KEYS.PASSWORD, appState.password);

        const settings = {
            showSpecialSections: appState.showSpecialSections,
            viewMode: appState.viewMode,
            sortBy: appState.sortBy,
            selectedSite: appState.selectedSite,
            selectedStar: appState.selectedStar,
            itemsPerPage: appState.itemsPerPage,
            openMoviesExternally: appState.openMoviesExternally,
            zoomLevel: appState.zoomLevel // حفظ مستوى التكبير
        };

        await localforage.setItem(STORAGE_KEYS.SETTINGS, settings);

        console.log('تم حفظ بيانات التطبيق بنجاح');
    } catch (error) {
        console.error('خطأ أثناء حفظ بيانات التطبيق:', error);
        showToast('حدث خطأ أثناء حفظ البيانات', 'error');
    }
}

// حفظ الصور المخبأة
async function saveCachedImages() {
    try {
        await localforage.setItem(STORAGE_KEYS.IMAGES, appState.cachedImages);
    } catch (error) {
        console.error('خطأ أثناء حفظ الصور المخبأة:', error);
    }
}

// تحديث عدادات الأقسام
function updateCategoriesCounts() {
    // إعادة تعيين العدادات
    appState.categories.main.forEach(category => category.count = 0);
    appState.categories.sub.forEach(category => category.count = 0);
    appState.categories.special.forEach(category => category.count = 0);
    appState.categories.specialSub.forEach(category => category.count = 0);

    // عد الأفلام في كل قسم
    appState.movies.forEach(movie => {
        if (movie.category && !movie.hidden) {
            const categoryType = getCategoryType(movie.category);
            if (categoryType) {
                const category = appState.categories[categoryType].find(cat => cat.id === movie.category);
                if (category) {
                    category.count++;
                }
            }
        }
    });

    // عد المسلسلات في قسم المسلسلات
    appState.series.forEach(series => {
        if (!series.hidden) {
            const category = appState.categories.main.find(cat => cat.id === 'series');
            if (category) {
                category.count++;
            }
        }
    });

    // تحديث عدد اختصارات المواقع في قسم مواقع الأفلام
    const movieSitesCategory = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (movieSitesCategory) {
        // حساب عدد المواقع في القائمة الرئيسية والمجلدات
        const shortcutsCount = movieSitesCategory.shortcuts ? movieSitesCategory.shortcuts.length : 0;
        const foldersCount = movieSitesCategory.folders ?
            movieSitesCategory.folders.reduce((total, folder) => total + (folder.sites ? folder.sites.length : 0), 0) : 0;
        movieSitesCategory.count = shortcutsCount + foldersCount;
    }

    // تحديث عدد "جميع الأفلام والمسلسلات"
    const totalGeneralMoviesCount = appState.movies.filter(movie => !movie.hidden && (
        appState.categories.main.some(cat => cat.id === movie.category) ||
        appState.categories.sub.some(cat => cat.id === movie.category)
    )).length;
    const totalSeriesCount = appState.series.filter(series => !series.hidden).length;
    const allCategory = appState.categories.main.find(cat => cat.id === 'all');
    if (allCategory) {
        allCategory.count = totalGeneralMoviesCount + totalSeriesCount;
    }

    // تحديث عد الأفلام في الأقسام الفرعية
    const allMovies = [...appState.movies, ...appState.series];
    allMovies.forEach(item => {
        if (item.subCategories && !item.hidden) {
            item.subCategories.forEach(subCat => {
                const subCategoryType = getCategoryType(subCat);
                if (subCategoryType) {
                    const category = appState.categories[subCategoryType].find(cat => cat.id === subCat);
                    if (category) {
                        category.count++;
                    }
                }
            });
        }
    });
}

// تحديد نوع القسم (رئيسي، فرعي، خاص، خاص فرعي)
function getCategoryType(categoryId) {
    if (appState.categories.main.some(cat => cat.id === categoryId)) {
        return 'main';
    } else if (appState.categories.sub.some(cat => cat.id === categoryId)) {
        return 'sub';
    } else if (appState.categories.special.some(cat => cat.id === categoryId)) {
        return 'special';
    } else if (appState.categories.specialSub.some(cat => cat.id === categoryId)) {
        return 'specialSub';
    }
    return null;
}

// عرض/إخفاء الأقسام الخاصة
function toggleSpecialSectionsVisibility() {
    const specialCategoriesEl = document.querySelector('.special-categories');
    const specialSubCategoriesEl = document.querySelector('.special-sub-categories');
    const dropzoneSection = document.getElementById('dropzone-section');

    if (appState.showSpecialSections) {
        specialCategoriesEl.classList.remove('hidden');
        specialSubCategoriesEl.classList.remove('hidden');
        if (appState.currentCategory === 's3' || appState.currentCategory === 's-sites') {
            dropzoneSection.classList.remove('hidden');
        }
    } else {
        specialCategoriesEl.classList.add('hidden');
        specialSubCategoriesEl.classList.add('hidden');
        dropzoneSection.classList.add('hidden');
    }
}

// عرض الأقسام في واجهة المستخدم
function renderCategories() {
    const mainCategoriesList = document.getElementById('main-categories-list');
    const subCategoriesList = document.getElementById('sub-categories-list');
    const specialCategoriesList = document.getElementById('special-categories-list');
    const specialSubCategoriesList = document.getElementById('special-sub-categories-list');

    // تفريغ القوائم
    mainCategoriesList.innerHTML = '';
    subCategoriesList.innerHTML = '';
    specialCategoriesList.innerHTML = '';
    specialSubCategoriesList.innerHTML = '';

    // عرض الأقسام الرئيسية
    appState.categories.main.forEach(category => {
        const li = document.createElement('li');
        li.dataset.category = category.id;
        li.innerHTML = `${category.name} <span class="counter">${category.count}</span>`;

        if (category.id === appState.currentCategory) {
            li.classList.add('active');
        }

        // تمييز قسم مواقع الأفلام بلون خاص
        if (category.id === 'movie-sites') {
            li.classList.add('movie-sites-category');
        }

        li.addEventListener('click', () => {
            displayMovies(category.id, 1);
        });

        // زر إضافة اختصار موقع للأقسام من نوع مواقع الأفلام
        if (category.id === 'movie-sites') {
            const buttonsContainer = document.createElement('div');
            buttonsContainer.style.display = 'flex';
            buttonsContainer.style.gap = '5px';
            buttonsContainer.style.marginRight = '10px';

            const addShortcutBtn = document.createElement('button');
            addShortcutBtn.textContent = 'إضافة موقع';
            addShortcutBtn.className = 'btn secondary';
            addShortcutBtn.onclick = (e) => {
                e.stopPropagation();
                const url = prompt('أدخل رابط الموقع:');
                if (url && url.trim()) {
                    const name = prompt('اسم الموقع (اختياري):') || url;
                    category.shortcuts = category.shortcuts || [];
                    category.shortcuts.push({ name, url });
                    saveAppData();
                    updateCategoriesCounts();
                    renderCategories();
                }
            };

            const addFolderBtn = document.createElement('button');
            addFolderBtn.textContent = 'إضافة مجلد';
            addFolderBtn.className = 'btn secondary';
            addFolderBtn.onclick = (e) => {
                e.stopPropagation();
                const folderName = prompt('أدخل اسم المجلد:');
                if (folderName && folderName.trim()) {
                    createSiteFolder(folderName);
                }
            };

            buttonsContainer.appendChild(addShortcutBtn);
            buttonsContainer.appendChild(addFolderBtn);
            li.appendChild(buttonsContainer);
        }

        mainCategoriesList.appendChild(li);
    });

    // عرض الأقسام الفرعية
    appState.categories.sub.forEach(category => {
        const li = document.createElement('li');
        li.dataset.category = category.id;
        li.innerHTML = `${category.name} <span class="counter">${category.count}</span>`;

        if (category.id === appState.currentCategory) {
            li.classList.add('active');
        }

        li.addEventListener('click', () => {
            displayMovies(category.id, 1);
        });

        subCategoriesList.appendChild(li);
    });

    // عرض الأقسام الخاصة الرئيسية
    appState.categories.special.forEach(category => {
        const li = document.createElement('li');
        li.dataset.category = category.id;
        li.innerHTML = `${category.name} <span class="counter">${category.count}</span>`;

        if (category.id === appState.currentCategory) {
            li.classList.add('active');
        }

        li.addEventListener('click', () => {
            displayMovies(category.id, 1);
            // عرض منطقة السحب والإفلات إذا كان القسم هو S3 أو S SITES
            const dropzoneSection = document.getElementById('dropzone-section');
            if ((category.id === 's3' || category.id === 's-sites') && appState.showSpecialSections) {
                dropzoneSection.classList.remove('hidden');
            } else {
                dropzoneSection.classList.add('hidden');
            }
        });

        specialCategoriesList.appendChild(li);
    });

    // عرض الأقسام الخاصة الفرعية
    appState.categories.specialSub.forEach(category => {
        const li = document.createElement('li');
        li.dataset.category = category.id;
        li.innerHTML = `${category.name} <span class="counter">${category.count}</span>`;

        if (category.id === appState.currentCategory) {
            li.classList.add('active');
        }

        li.addEventListener('click', () => {
            displayMovies(category.id);
        });

        specialSubCategoriesList.appendChild(li);
    });

    // تحديث سلاسل الاختيار في مختلف أجزاء التطبيق
    updateCategorySelectOptions();
}

// تحديث خيارات الأقسام في جميع القوائم المنسدلة
function updateCategorySelectOptions() {
    const selects = [
        document.getElementById('movie-category'),
        document.getElementById('edit-movie-category'),
        document.getElementById('import-category'),
        document.getElementById('manage-category-select'),
        document.getElementById('filter-category'),
        document.getElementById('target-category-select')
    ];

    selects.forEach(select => {
        if (select) {
            // حفظ القيمة الحالية
            const currentValue = select.value;
            select.innerHTML = '';

            // إضافة الأقسام الرئيسية
            const mainOptgroup = document.createElement('optgroup');
            mainOptgroup.label = 'الأقسام العامة الرئيسية';

            appState.categories.main.forEach(category => {
                if (category.id !== 'all') { // استثناء "جميع الأفلام والمسلسلات" من بعض القوائم
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    mainOptgroup.appendChild(option);
                }
            });

            select.appendChild(mainOptgroup);

            // إضافة الأقسام الفرعية العامة
            const subOptgroup = document.createElement('optgroup');
            subOptgroup.label = 'الأقسام العامة الفرعية';

            appState.categories.sub.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                subOptgroup.appendChild(option);
            });

            select.appendChild(subOptgroup);

            // إضافة الأقسام الخاصة الرئيسية
            if (appState.showSpecialSections) {
                const specialOptgroup = document.createElement('optgroup');
                specialOptgroup.label = 'الأقسام الخاصة الرئيسية';

                appState.categories.special.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    specialOptgroup.appendChild(option);
                });

                select.appendChild(specialOptgroup);

                // إضافة الأقسام الفرعية الخاصة
                const specialSubOptgroup = document.createElement('optgroup');
                specialSubOptgroup.label = 'الأقسام الخاصة الفرعية';

                appState.categories.specialSub.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    specialSubOptgroup.appendChild(option);
                });

                select.appendChild(specialSubOptgroup);
            }

            // إعادة تعيين القيمة المحددة إذا كانت موجودة
            if (currentValue && [...select.options].some(opt => opt.value === currentValue)) {
                select.value = currentValue;
            }
        }
    });

    // تحديث خيارات الأقسام الفرعية في مودال إضافة إلى قسم فرعي
    const subcategorySelect = document.getElementById('subcategory-select');
    if (subcategorySelect) {
        subcategorySelect.innerHTML = '';

        // التحقق من نوع القسم الحالي
        const isSpecialCategory = appState.categories.special.some(cat => cat.id === appState.currentCategory);
        const isRCategory = appState.currentCategory === 'r1' || appState.currentCategory === 'r2';

        if (isSpecialCategory) {
            // خيارات للأقسام الخاصة
            appState.categories.specialSub.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                subcategorySelect.appendChild(option);
            });
        } else if (isRCategory) {
            // خيارات لأقسام R مع خيارات إضافية
            [...appState.categories.sub, ...appState.categories.specialSub].forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                subcategorySelect.appendChild(option);
            });
        } else {
            // خيارات للأقسام العادية
            appState.categories.sub.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                subcategorySelect.appendChild(option);
            });
        }
    }
}

// عرض الأفلام في القسم المحدد
function displayMovies(categoryId, page = 1) {
    // منع الاستدعاءات المتكررة
    if (displayMovies._isRunning) return;
    displayMovies._isRunning = true;

    try {
        // التمرير فوراً عند بداية التحديث
        scrollToTopImmediate();

        appState.currentCategory = categoryId;
        appState.currentPage = page;

        _displayMoviesCore(categoryId, page);
    } catch (error) {
        console.error('خطأ في عرض الأفلام:', error);
    } finally {
        displayMovies._isRunning = false;
    }
}

// عرض الأفلام بدون التمرير إلى الأعلى
function displayMoviesWithoutScroll(categoryId, page = 1) {
    // منع الاستدعاءات المتكررة
    if (displayMoviesWithoutScroll._isRunning) return;
    displayMoviesWithoutScroll._isRunning = true;

    try {
        appState.currentCategory = categoryId;
        appState.currentPage = page;

        _displayMoviesCore(categoryId, page);
    } catch (error) {
        console.error('خطأ في عرض الأفلام:', error);
    } finally {
        displayMoviesWithoutScroll._isRunning = false;
    }
}

// الوظيفة الأساسية لعرض الأفلام
function _displayMoviesCore(categoryId, page) {
    // تحديث عنصر القسم النشط
    document.querySelectorAll('.categories-section li').forEach(li => {
        li.classList.remove('active');
        if (li.dataset.category === categoryId) {
            li.classList.add('active');
        }
    });

    // تحديث عنوان القسم الحالي
    const currentCategoryElement = document.getElementById('current-category');
    const categoryObj = [...appState.categories.main, ...appState.categories.sub,
                         ...appState.categories.special, ...appState.categories.specialSub]
                        .find(cat => cat.id === categoryId);

    if (categoryObj) {
        currentCategoryElement.textContent = categoryObj.name;
        // تحديث عنوان الصفحة في المتصفح
        const newTitle = `${categoryObj.name} - New Koktil-aflam v25`;
        document.title = newTitle;
        appState.currentPageTitle = newTitle;
    }

    // إظهار/إخفاء خيار الترتيب حسب النجم عندما يكون القسم الحالي هو أفلام النجوم
    const starSortOption = document.querySelector('.star-sort-option');
    if (starSortOption) {
        if (categoryId === 'stars') {
            starSortOption.classList.remove('hidden');
        } else {
            starSortOption.classList.add('hidden');
        }
    }

    // إعادة تعيين الفلاتر والترتيب عند تغيير القسم
    const oldCategory = appState.currentCategory;
    if (oldCategory !== categoryId) {
        appState.selectedSite = '';
        appState.selectedStar = '';
        // إعادة تعيين الترتيب إلى الترتيب حسب الاسم
        appState.sortBy = 'name';

        // تحديث القائمة المنسدلة للترتيب
        const sortSelect = document.getElementById('sort-options');
        if (sortSelect) {
            sortSelect.value = 'name';
        }

        // تحديث حقل الإدخال الرقمي للترتيب
        const sortInput = document.getElementById('sort-options-input');
        if (sortInput) {
            sortInput.value = '1';
        }

        // تحديث حقول الفلاتر
        const siteFilter = document.getElementById('site-filter');
        const starFilter = document.getElementById('star-filter');
        const siteInput = document.getElementById('site-filter-input');
        const starInput = document.getElementById('star-filter-input');

        if (siteFilter) siteFilter.value = '';
        if (starFilter) starFilter.value = '';
        if (siteInput) siteInput.value = '0';
        if (starInput) starInput.value = '0';
    }

    // تحديث رؤية خانات الفلترة
    updateFilterVisibility();

    // الحصول على الأفلام المناسبة للقسم

    let items = [];
    if (categoryId === 'all') {
        // جميع الأفلام والمسلسلات من الأقسام العامة (رئيسية وفرعية)
        items = [...appState.movies, ...appState.series].filter(item => !item.hidden && (
            appState.categories.main.some(cat => cat.id === item.category) ||
            appState.categories.sub.some(cat => cat.id === item.category)
        ));
    } else if (categoryId === 'series') {
        // المسلسلات فقط
        items = appState.series.filter(item => !item.hidden);
    } else if (categoryId === 'movie-sites') {
        // قسم مواقع الأفلام: عرض الاختصارات والمجلدات
        const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
        items = [];

        if (movieSitesCat) {
            // إضافة المجلدات أولاً (فقط المجلدات غير المخفية)
            if (movieSitesCat.folders && movieSitesCat.folders.length > 0) {
                movieSitesCat.folders.forEach((folder, folderIdx) => {
                    if (!folder.hidden) {
                        items.push({
                            id: 'folder-' + folder.id,
                            name: folder.name,
                            img: '', // يمكن إضافة أيقونة مجلد لاحقاً
                            href: '#',
                            category: 'movie-sites',
                            isFolder: true,
                            folder: folder,
                            _folderIndex: folderIdx
                        });
                    }
                });
            }

            // إضافة المواقع في القائمة الرئيسية
            if (movieSitesCat.shortcuts && movieSitesCat.shortcuts.length > 0) {
                const shortcuts = movieSitesCat.shortcuts.map((shortcut, idx) => ({
                    id: 'site-' + idx,
                    name: shortcut.name || shortcut.title || shortcut.url,
                    img: shortcut.img || '',
                    href: shortcut.url,
                    category: 'movie-sites',
                    isSiteShortcut: true,
                    _shortcutIndex: idx
                }));
                items.push(...shortcuts);
            }
        }
    } else {
        // الأقسام الأخرى
        const isMainCategory = appState.categories.main.some(cat => cat.id === categoryId);
        const isSubCategory = appState.categories.sub.some(cat => cat.id === categoryId);
        const isSpecialSubCategory = appState.categories.specialSub.some(cat => cat.id === categoryId);

        if (isMainCategory) {
            // القسم الرئيسي
            items = appState.movies.filter(movie => movie.category === categoryId && !movie.hidden);
        } else if (isSubCategory || isSpecialSubCategory) {
            // القسم الفرعي (عام أو خاص)
            items = [...appState.movies, ...appState.series].filter(item =>
                item.subCategories &&
                item.subCategories.includes(categoryId) &&
                !item.hidden
            );
        } else {
            // القسم الخاص
            items = appState.movies.filter(movie => movie.category === categoryId && !movie.hidden);
        }
    }

    // ترتيب وفلترة الأفلام
    items = sortItems(items, appState.sortBy);
    items = applyFilters(items);

    // تقسيم الأفلام إلى صفحات
    const startIndex = (appState.currentPage - 1) * appState.itemsPerPage;
    const endIndex = startIndex + appState.itemsPerPage;
    const paginatedItems = items.slice(startIndex, endIndex);

    // عرض الأفلام في الواجهة
    renderMovies(paginatedItems, categoryId, startIndex);

    // تحديث أزرار الصفحات
    updatePagination(items.length);

    // تطبيق مستوى التكبير
    applyZoom();

    // تحديد ما إذا كان يجب إظهار منطقة السحب والإفلات
    const dropzoneSection = document.getElementById('dropzone-section');
    if ((categoryId === 's3' || categoryId === 's-sites') && appState.showSpecialSections) {
        dropzoneSection.classList.remove('hidden');
        console.log(`إظهار منطقة السحب والإفلات للقسم: ${categoryId}`);

        // إعادة تهيئة مناطق السحب والإفلات للتأكد من عملها
        setTimeout(() => {
            setupDropZones();
        }, 100);
    } else {
        dropzoneSection.classList.add('hidden');
        console.log(`إخفاء منطقة السحب والإفلات للقسم: ${categoryId}`);
    }

    // حفظ البيانات
    saveAppData();
}

// ترتيب العناصر (الأفلام/المسلسلات)
function sortItems(items, sortType) {
    const sortedItems = [...items];

    switch (sortType) {
        case 'name':
            sortedItems.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
            break;
        case 'site':
            sortedItems.sort((a, b) => {
                // استخراج اسم الموقع من الرابط
                const getSite = (url) => {
                    if (!url) return '';
                    try {
                        const domain = new URL(url).hostname;
                        return domain;
                    } catch (e) {
                        return url;
                    }
                };

                const siteA = getSite(a.href);
                const siteB = getSite(b.href);
                return siteA.localeCompare(siteB, 'ar');
            });
            break;
        case 'date':
            // افتراض أن التاريخ الأحدث يأتي أولاً
            sortedItems.sort((a, b) => {
                const dateA = a.addedDate ? new Date(a.addedDate) : new Date(0);
                const dateB = b.addedDate ? new Date(b.addedDate) : new Date(0);
                return dateB - dateA;
            });
            break;
        case 'date-asc':
            // ترتيب تصاعدي حسب التاريخ (الأقدم أولاً)
            sortedItems.sort((a, b) => {
                const dateA = a.addedDate ? new Date(a.addedDate) : new Date(0);
                const dateB = b.addedDate ? new Date(b.addedDate) : new Date(0);
                return dateA - dateB;
            });
            break;
        case 'star':
            // فقط لقسم النجوم
            sortedItems.sort((a, b) => {
                const starA = a.starName || '';
                const starB = b.starName || '';
                return starA.localeCompare(starB, 'ar');
            });
            break;
    }

    return sortedItems;
}

// تحديث رؤية خانات الفلترة
function updateFilterVisibility() {
    const siteFilterContainer = document.getElementById('site-filter-container');
    const starFilterContainer = document.getElementById('star-filter-container');

    if (!siteFilterContainer || !starFilterContainer) return;

    // إخفاء جميع خانات الفلترة أولاً
    siteFilterContainer.classList.add('hidden');
    starFilterContainer.classList.add('hidden');

    // إظهار الخانة المناسبة حسب نوع الترتيب
    if (appState.sortBy === 'site') {
        siteFilterContainer.classList.remove('hidden');
        populateSiteFilterMain();
    } else if (appState.sortBy === 'star' && appState.currentCategory === 'stars') {
        starFilterContainer.classList.remove('hidden');
        populateStarFilter();
    }
}

// تعبئة خانة فلترة المواقع في الواجهة الرئيسية
function populateSiteFilterMain() {
    const siteFilter = document.getElementById('site-filter');
    if (!siteFilter) return;

    const currentValue = siteFilter.value;

    // تفريغ الخيارات
    siteFilter.innerHTML = '<option value="">جميع المواقع</option>';

    // الحصول على جميع المواقع الفريدة من الأفلام في القسم الحالي
    const sites = new Set();

    try {
        let items = [];
        if (appState.currentCategory === 'all') {
            items = [...appState.movies, ...appState.series].filter(item => !item.hidden);
        } else if (appState.currentCategory === 'series') {
            items = appState.series.filter(item => !item.hidden);
        } else {
            const isMainCategory = appState.categories.main.some(cat => cat.id === appState.currentCategory);
            const isSubCategory = appState.categories.sub.some(cat => cat.id === appState.currentCategory) ||
                                 appState.categories.specialSub.some(cat => cat.id === appState.currentCategory);

            if (isMainCategory) {
                items = appState.movies.filter(movie => movie.category === appState.currentCategory && !movie.hidden);
            } else if (isSubCategory) {
                items = [...appState.movies, ...appState.series].filter(item =>
                    item.subCategories &&
                    item.subCategories.includes(appState.currentCategory) &&
                    !item.hidden
                );
            } else {
                items = appState.movies.filter(movie => movie.category === appState.currentCategory && !movie.hidden);
            }
        }

        items.forEach(item => {
            if (item.href) {
                try {
                    const site = getSiteFromUrl(item.href);
                    if (site) sites.add(site);
                } catch (e) {
                    // تجاهل الروابط غير الصحيحة
                }
            }
        });

        // إضافة الخيارات مع أرقام
        const sitesArray = Array.from(sites).sort();
        sitesArray.forEach((site, index) => {
            const option = document.createElement('option');
            option.value = site;
            option.textContent = `${index + 1}. ${site}`;
            siteFilter.appendChild(option);
        });

        // استعادة القيمة المحددة إذا كانت موجودة
        if (currentValue && Array.from(siteFilter.options).some(opt => opt.value === currentValue)) {
            siteFilter.value = currentValue;
        } else if (appState.selectedSite && Array.from(siteFilter.options).some(opt => opt.value === appState.selectedSite)) {
            siteFilter.value = appState.selectedSite;
        }
    } catch (error) {
        console.error('خطأ في تعبئة فلتر المواقع:', error);
    }
}

// تعبئة خانة فلترة النجوم
function populateStarFilter() {
    const starFilter = document.getElementById('star-filter');
    if (!starFilter) return;

    const currentValue = starFilter.value;

    // تفريغ الخيارات
    starFilter.innerHTML = '<option value="">جميع النجوم</option>';

    try {
        // الحصول على جميع أسماء النجوم الفريدة من أفلام النجوم
        const stars = new Set();

        const starsMovies = appState.movies.filter(movie => movie.category === 'stars' && !movie.hidden);
        starsMovies.forEach(movie => {
            if (movie.starName) {
                stars.add(movie.starName);
            }
        });

        // إضافة الخيارات مع أرقام
        const starsArray = Array.from(stars).sort();
        starsArray.forEach((star, index) => {
            const option = document.createElement('option');
            option.value = star;
            option.textContent = `${index + 1}. ${star}`;
            starFilter.appendChild(option);
        });

        // استعادة القيمة المحددة إذا كانت موجودة
        if (currentValue && Array.from(starFilter.options).some(opt => opt.value === currentValue)) {
            starFilter.value = currentValue;
        } else if (appState.selectedStar && Array.from(starFilter.options).some(opt => opt.value === appState.selectedStar)) {
            starFilter.value = appState.selectedStar;
        }
    } catch (error) {
        console.error('خطأ في تعبئة فلتر النجوم:', error);
    }
}

// تطبيق الفلاتر على الأفلام
function applyFilters(items) {
    let filteredItems = [...items];

    // فلترة حسب الموقع
    if (appState.selectedSite && appState.sortBy === 'site') {
        filteredItems = filteredItems.filter(item => {
            const site = getSiteFromUrl(item.href);
            return site === appState.selectedSite;
        });
    }

    // فلترة حسب النجم
    if (appState.selectedStar && appState.sortBy === 'star' && appState.currentCategory === 'stars') {
        filteredItems = filteredItems.filter(item => {
            return item.starName === appState.selectedStar;
        });
    }

    return filteredItems;
}

// تحديث أزرار الصفحات
function updatePagination(totalItems) {
    const totalPages = Math.ceil(totalItems / appState.itemsPerPage);
    const currentPageElement = document.getElementById('current-page');
    const totalPagesElement = document.getElementById('total-pages');
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    const paginationControls = document.querySelector('.pagination-controls');

    // تحديث عناصر التنقل في الرأس أيضاً
    const headerCurrentPageElement = document.getElementById('header-current-page');
    const headerTotalPagesElement = document.getElementById('header-total-pages');
    const headerPrevButton = document.getElementById('header-prev-page');
    const headerNextButton = document.getElementById('header-next-page');

    currentPageElement.textContent = appState.currentPage;
    totalPagesElement.textContent = totalPages;
    headerCurrentPageElement.textContent = appState.currentPage;
    headerTotalPagesElement.textContent = totalPages;

    // تعطيل/تفعيل أزرار الصفحات
    prevButton.disabled = appState.currentPage <= 1;
    nextButton.disabled = appState.currentPage >= totalPages;
    headerPrevButton.disabled = appState.currentPage <= 1;
    headerNextButton.disabled = appState.currentPage >= totalPages;

    // إضافة مستمعي الأحداث للأزرار السفلية
    prevButton.onclick = () => {
        if (appState.currentPage > 1) {
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage - 1);
        }
    };

    nextButton.onclick = () => {
        if (appState.currentPage < totalPages) {
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage + 1);
        }
    };

    // إضافة مستمعي الأحداث للأزرار في الرأس
    headerPrevButton.onclick = () => {
        if (appState.currentPage > 1) {
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage - 1);
        }
    };

    headerNextButton.onclick = () => {
        if (appState.currentPage < totalPages) {
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage + 1);
        }
    };

    // إزالة أزرار الصفحات السابقة إذا وجدت
    const existingPageButtons = document.querySelectorAll('.page-number-btn');
    existingPageButtons.forEach(btn => btn.remove());

    // إضافة أزرار الصفحات (10 صفحات سابقة و10 صفحات تالية)
    if (totalPages > 1) {
        // تحديد نطاق الصفحات التي سيتم عرضها
        let startPage = Math.max(1, appState.currentPage - 10);
        let endPage = Math.min(totalPages, appState.currentPage + 10);

        // إنشاء أزرار الصفحات
        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.classList.add('page-number-btn');
            pageButton.textContent = i;

            // تمييز الصفحة الحالية
            if (i === appState.currentPage) {
                pageButton.classList.add('current');
            }

            // إضافة مستمع الحدث للانتقال إلى الصفحة
            pageButton.addEventListener('click', () => {
                if (i !== appState.currentPage) {
                    // التمرير الفوري عند النقر
                    scrollToTopImmediate();
                    displayMovies(appState.currentCategory, i);
                }
            });

            // إضافة الزر بين زر السابق وزر التالي
            paginationControls.insertBefore(pageButton, nextButton);
        }
    }

    // إخفاء/إظهار أيقونات التنقل في الرأس حسب الحاجة
    updateHeaderNavigationVisibility(totalPages);
}

// دالة لإخفاء/إظهار أيقونات التنقل في الرأس
function updateHeaderNavigationVisibility(totalPages) {
    const headerNavigation = document.querySelector('.header-navigation');

    if (!headerNavigation) return;

    if (totalPages <= 1) {
        // إخفاء أيقونات التنقل إذا كانت هناك صفحة واحدة فقط أو لا توجد صفحات
        headerNavigation.style.display = 'none';
    } else {
        // إظهار أيقونات التنقل إذا كانت هناك أكثر من صفحة واحدة
        headerNavigation.style.display = 'flex';

        // إضافة تأثير انتقالي عند الظهور
        headerNavigation.style.opacity = '0';
        headerNavigation.style.transform = 'scale(0.8)';

        setTimeout(() => {
            headerNavigation.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            headerNavigation.style.opacity = '1';
            headerNavigation.style.transform = 'scale(1)';
        }, 50);
    }
}

// التمرير الفوري إلى أعلى الصفحة
function scrollToTopImmediate() {
    // التمرير الفوري لأعلى الصفحة
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
}

// التمرير إلى أعلى الصفحة
function scrollToTop() {
    // الحصول على منطقة المحتوى
    const contentSection = document.getElementById('content-section');
    const header = document.getElementById('app-header');

    if (contentSection && header) {
        // حساب الموقع المطلوب (أعلى منطقة المحتوى مع مراعاة ارتفاع الرأس)
        const headerHeight = header.offsetHeight;
        const targetPosition = contentSection.offsetTop - headerHeight - 10; // 10px مسافة إضافية

        // التمرير الفوري
        window.scrollTo(0, Math.max(0, targetPosition));

        // ثم التمرير السلس للتأكد
        setTimeout(() => {
            window.scrollTo({
                top: Math.max(0, targetPosition),
                behavior: 'smooth'
            });
        }, 10);
    } else {
        // التمرير العادي إذا لم نجد العناصر
        scrollToTopImmediate();
    }
}

// عرض الأفلام في الواجهة
function renderMovies(items, categoryId, startIndex) {
    const container = document.getElementById('movies-container');

    // تحديد نمط العرض
    container.className = appState.viewMode === 'grid' ? 'grid-view' : 'list-view';

    // تفريغ الحاوية
    container.innerHTML = '';

    // التحقق مما إذا كان القسم هو الأفلام الخاصة R
    const isRCategory = categoryId === 'r1' || categoryId === 'r2';
    const isSpecialCategory = appState.categories.special.some(cat => cat.id === categoryId);
    const isSubCategory = appState.categories.sub.some(cat => cat.id === categoryId) ||
                         appState.categories.specialSub.some(cat => cat.id === categoryId);

    // إذا لم تكن هناك عناصر
    if (items.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد أفلام في هذا القسم</div>';
        return;
    }


    // عرض كل فيلم/مسلسل أو بطاقة موقع
    items.forEach((item, index) => {
        const movieCard = document.createElement('div');
        movieCard.className = 'movie-card';

        // إضافة class خاص لبطاقات أفلام النجوم لإرجاعها للمقياس القديم
        if (categoryId === 'stars') {
            movieCard.classList.add('stars-category');
        }

        movieCard.dataset.id = item.id;

        // اسم الصورة من الرابط
        const imgFilename = getImageFilenameFromUrl(item.img);
        // استخدام الصورة المخبأة إذا كانت موجودة
        const imgSrc = appState.cachedImages[imgFilename] || item.img;

        // تحديد أزرار التحكم المناسبة بناءً على نوع القسم
        let favButtonHtml = '';
        if (isSubCategory) {
            // زر الحذف من القسم الفرعي
            favButtonHtml = `<button class="movie-remove-btn" data-id="${item.id}" data-subcategory="${categoryId}">
                                <i class="fas fa-trash-alt"></i>
                             </button>`;
        } else {
            // زر التمييز للإضافة إلى قسم فرعي
            const isFavorited = item.subCategories && item.subCategories.length > 0;
            favButtonHtml = `<button class="movie-favorite-btn ${isFavorited ? 'marked' : ''}" data-id="${item.id}">
                                <i class="fas fa-star"></i>
                             </button>`;
        }


        // أزرار إضافية (تشغيل/تعديل/حذف)
        let playBtnHtml = `<button class="movie-play-btn" data-id="${item.id}" data-href="${item.href}"><i class="fas fa-play"></i></button>`;
        let editBtnHtml = `<button class="movie-edit-btn" data-id="${item.id}"><i class="fas fa-ellipsis-v"></i></button>`;
        let deleteBtnHtml = `<button class="movie-delete-btn" data-id="${item.id}"><i class="fas fa-trash"></i></button>`;

        // إذا كان القسم هو مواقع الأفلام أو العنصر اختصار موقع، أضف زر زيارة الموقع فقط، واحتفظ بأزرار التعديل والحذف
        if (categoryId === 'movie-sites' || item.isSiteShortcut) {
            if (item.isFolder) {
                // بطاقة مجلد
                playBtnHtml = `<button class="folder-open-btn" data-folder-id="${item.folder.id}"><i class="fas fa-folder-open"></i></button>`;
            } else {
                // بطاقة موقع
                playBtnHtml = `
                    <button class="site-visit-internal-btn" data-id="${item.id}" data-url="${item.href}" data-name="${item.name.replace(/"/g, '&quot;')}" title="فتح داخل التطبيق">
                        <i class="fas fa-desktop"></i>
                    </button>
                    <button class="site-visit-external-btn" data-url="${item.href}" title="فتح خارجياً">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                `;

                // إضافة زر نقل إلى مجلد للمواقع في القائمة الرئيسية
                if (item.isSiteShortcut && typeof item._shortcutIndex !== 'undefined') {
                    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
                    if (movieSitesCat && movieSitesCat.folders && movieSitesCat.folders.length > 0) {
                        playBtnHtml += `<button class="move-to-folder-btn" data-site-index="${item._shortcutIndex}"><i class="fas fa-folder-plus"></i></button>`;
                    }
                }
            }
        }

        movieCard.innerHTML = `
            <div class="movie-top-controls">
                <span class="movie-number">${startIndex + index + 1}</span>
                <div class="movie-edit-controls">
                    ${deleteBtnHtml}
                    ${editBtnHtml}
                </div>
            </div>
            <img src="${imgSrc || 'https://cdn-icons-png.flaticon.com/512/561/561127.png'}" alt="${item.name}" class="movie-image" onerror="this.src='https://cdn-icons-png.flaticon.com/512/561/561127.png'">
            <div class="movie-details">
                <h3>${item.name}</h3>
                <div class="movie-site">${getSiteFromUrl(item.href) || ''}</div>
            </div>
            <div class="movie-bottom-controls">
                ${playBtnHtml}
                ${favButtonHtml}
            </div>
        `;

        // إضافة فئة CSS خاصة للمجلدات والمواقع
        if (categoryId === 'movie-sites' || item.isSiteShortcut) {
            if (item.isFolder) {
                movieCard.classList.add('folder-card');
            } else {
                movieCard.classList.add('site-shortcut-card');
            }
        }

        // إضافة إمكانية السحب والإفلات للمواقع في القائمة الرئيسية
        if (categoryId === 'movie-sites' && item.isSiteShortcut && typeof item._shortcutIndex !== 'undefined') {
            movieCard.draggable = true;
            movieCard.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', JSON.stringify({
                    type: 'main-site',
                    siteIndex: item._shortcutIndex
                }));
                movieCard.style.opacity = '0.5';
            });

            movieCard.addEventListener('dragend', () => {
                movieCard.style.opacity = '1';
            });
        }

        // إضافة بطاقة الفيلم/الموقع إلى الحاوية
        container.appendChild(movieCard);

        // تحميل وتخزين الصورة محليًا إذا لم تكن موجودة بالفعل
        if (!appState.cachedImages[imgFilename] && item.img) {
            cacheImage(item.img, imgFilename);
        }
    });
    // زر فتح الموقع داخل التطبيق
    document.querySelectorAll('.site-visit-internal-btn').forEach(button => {
        // إزالة event listener السابق إن وجد
        button.removeEventListener('click', button._internalClickHandler);

        // إنشاء handler جديد
        button._internalClickHandler = (e) => {
            e.stopPropagation();
            console.log('تم النقر على زر الفتح الداخلي');
            const url = button.dataset.url;
            const name = button.dataset.name;
            const id = button.dataset.id;
            console.log(`البيانات: URL=${url}, Name=${name}, ID=${id}`);

            if (url) {
                // إنشاء كائن موقع مؤقت
                const siteItem = {
                    id: id,
                    name: name,
                    href: url,
                    img: '',
                    category: 'movie-sites',
                    isMainSite: true
                };
                console.log('فتح مودال الموقع:', siteItem);
                openSitePlayModal(siteItem);
            } else {
                console.error('لا يوجد URL للموقع');
                showToast('خطأ: لا يوجد رابط للموقع', 'error');
            }
        };

        // إضافة event listener الجديد
        button.addEventListener('click', button._internalClickHandler);
    });

    // زر فتح الموقع خارجياً
    document.querySelectorAll('.site-visit-external-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const url = button.dataset.url;
            if (url) {
                window.open(url, '_blank');
            }
        });
    });

    // زر نقل الموقع إلى مجلد
    document.querySelectorAll('.move-to-folder-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const siteIndex = parseInt(button.dataset.siteIndex);
            showFolderSelectionModal(siteIndex);
        });
    });

    // زر فتح المجلد
    document.querySelectorAll('.folder-open-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const folderId = button.dataset.folderId;
            showFolderContents(folderId);
        });
    });

    // إضافة مستمعي الأحداث
    addMovieCardEventListeners();

    // فرض إعادة رسم المحتوى
    container.offsetHeight;
}

// الحصول على اسم الملف من URL
function getImageFilenameFromUrl(url) {
    if (!url) return '';
    try {
        const filename = url.split('/').pop().split('?')[0];
        return filename || url;
    } catch (e) {
        return url;
    }
}

// تخزين الصورة محليًا
async function cacheImage(imageUrl, filename) {
    try {
        const response = await fetch(imageUrl);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const blob = await response.blob();
        const reader = new FileReader();

        reader.onloadend = function() {
            appState.cachedImages[filename] = reader.result;
            saveCachedImages();
        };

        reader.readAsDataURL(blob);
    } catch (error) {
        console.error('Error caching image:', error);
    }
}

// الحصول على اسم الموقع من URL
function getSiteFromUrl(url) {
    if (!url) return '';
    try {
        const domain = new URL(url).hostname;
        return domain.replace('www.', '');
    } catch (e) {
        return '';
    }
}

// إدارة مجلدات مواقع الأفلام
function createSiteFolder(folderName) {
    if (!folderName || !folderName.trim()) {
        showToast('يرجى إدخال اسم المجلد', 'warning');
        return;
    }

    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    // التحقق من عدم وجود مجلد بنفس الاسم
    if (movieSitesCat.folders.some(folder => folder.name === folderName.trim())) {
        showToast('يوجد مجلد بهذا الاسم بالفعل', 'warning');
        return;
    }

    // إنشاء المجلد الجديد
    const newFolder = {
        id: generateUniqueId(),
        name: folderName.trim(),
        sites: [],
        expanded: true
    };

    movieSitesCat.folders.push(newFolder);
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم إنشاء مجلد "${folderName}" بنجاح`, 'success');
}

function deleteSiteFolder(folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folderIndex = movieSitesCat.folders.findIndex(folder => folder.id === folderId);
    if (folderIndex === -1) return;

    const folder = movieSitesCat.folders[folderIndex];

    // نقل المواقع من المجلد إلى القائمة الرئيسية
    if (folder.sites && folder.sites.length > 0) {
        movieSitesCat.shortcuts.push(...folder.sites);
    }

    // حذف المجلد
    movieSitesCat.folders.splice(folderIndex, 1);
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم حذف مجلد "${folder.name}" ونقل محتوياته إلى القائمة الرئيسية`, 'success');
}

function renameSiteFolder(folderId, newName) {
    if (!newName || !newName.trim()) {
        showToast('يرجى إدخال اسم صحيح للمجلد', 'warning');
        return;
    }

    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(folder => folder.id === folderId);
    if (!folder) return;

    // التحقق من عدم وجود مجلد آخر بنفس الاسم
    if (movieSitesCat.folders.some(f => f.id !== folderId && f.name === newName.trim())) {
        showToast('يوجد مجلد بهذا الاسم بالفعل', 'warning');
        return;
    }

    folder.name = newName.trim();
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم تغيير اسم المجلد إلى "${newName}" بنجاح`, 'success');
}

function toggleFolderExpansion(folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(folder => folder.id === folderId);
    if (!folder) return;

    folder.expanded = !folder.expanded;
    saveAppData();
    renderCategories();
}

function moveSiteToFolder(siteIndex, folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    if (siteIndex < 0 || siteIndex >= movieSitesCat.shortcuts.length) return;

    const site = movieSitesCat.shortcuts[siteIndex];
    const folder = movieSitesCat.folders.find(f => f.id === folderId);

    if (!folder) return;

    // نقل الموقع من القائمة الرئيسية إلى المجلد
    movieSitesCat.shortcuts.splice(siteIndex, 1);
    folder.sites.push(site);

    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم نقل الموقع إلى مجلد "${folder.name}" بنجاح`, 'success');
}

function moveSiteFromFolder(folderId, siteIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder || siteIndex < 0 || siteIndex >= folder.sites.length) return;

    const site = folder.sites[siteIndex];

    // نقل الموقع من المجلد إلى القائمة الرئيسية
    folder.sites.splice(siteIndex, 1);
    movieSitesCat.shortcuts.push(site);

    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم نقل الموقع من مجلد "${folder.name}" إلى القائمة الرئيسية`, 'success');
}

function moveSiteBetweenFolders(sourceFolderId, siteIndex, targetFolderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const sourceFolder = movieSitesCat.folders.find(f => f.id === sourceFolderId);
    const targetFolder = movieSitesCat.folders.find(f => f.id === targetFolderId);

    if (!sourceFolder || !targetFolder || siteIndex < 0 || siteIndex >= sourceFolder.sites.length) return;

    const site = sourceFolder.sites[siteIndex];

    // نقل الموقع من المجلد المصدر إلى المجلد الهدف
    sourceFolder.sites.splice(siteIndex, 1);
    targetFolder.sites.push(site);

    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم نقل الموقع من مجلد "${sourceFolder.name}" إلى مجلد "${targetFolder.name}"`, 'success');
}

// عرض نافذة اختيار المجلد
function showFolderSelectionModal(siteIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat || !movieSitesCat.folders || movieSitesCat.folders.length === 0) {
        showToast('لا توجد مجلدات متاحة. قم بإنشاء مجلد أولاً.', 'info');
        return;
    }

    // إنشاء نافذة منبثقة لاختيار المجلد
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.style.zIndex = '10000';

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.maxWidth = '400px';

    const title = document.createElement('h3');
    title.textContent = 'اختر المجلد لنقل الموقع إليه';
    title.style.marginBottom = '20px';

    const foldersList = document.createElement('div');
    foldersList.style.maxHeight = '300px';
    foldersList.style.overflowY = 'auto';
    foldersList.style.marginBottom = '20px';

    movieSitesCat.folders.forEach(folder => {
        const folderItem = document.createElement('div');
        folderItem.className = 'folder-selection-item';
        folderItem.style.cssText = `
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        `;

        folderItem.innerHTML = `
            <i class="fas fa-folder" style="color: #ffa726; margin-left: 10px;"></i>
            <span>${folder.name}</span>
            <span style="margin-right: auto; color: #666;">(${folder.sites ? folder.sites.length : 0} مواقع)</span>
        `;

        folderItem.addEventListener('mouseenter', () => {
            folderItem.style.backgroundColor = '#f5f5f5';
        });

        folderItem.addEventListener('mouseleave', () => {
            folderItem.style.backgroundColor = 'transparent';
        });

        folderItem.addEventListener('click', () => {
            moveSiteToFolder(siteIndex, folder.id);
            document.body.removeChild(modal);
        });

        foldersList.appendChild(folderItem);
    });

    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = 'إلغاء';
    cancelBtn.className = 'btn secondary';
    cancelBtn.style.width = '100%';
    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modalContent.appendChild(title);
    modalContent.appendChild(foldersList);
    modalContent.appendChild(cancelBtn);
    modal.appendChild(modalContent);

    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    document.body.appendChild(modal);
}

// عرض محتويات المجلد
function showFolderContents(folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder) return;

    // إنشاء نافذة منبثقة لعرض محتويات المجلد
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.style.zIndex = '10000';

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.maxWidth = '600px';
    modalContent.style.maxHeight = '80vh';
    modalContent.style.overflow = 'auto';

    const header = document.createElement('div');
    header.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e0cfff;
    `;

    const title = document.createElement('h3');
    title.innerHTML = `<i class="fas fa-folder" style="color: #ffa726; margin-left: 10px;"></i>${folder.name}`;
    title.style.margin = '0';

    const folderActions = document.createElement('div');
    folderActions.style.display = 'flex';
    folderActions.style.gap = '10px';

    const renameBtn = document.createElement('button');
    renameBtn.innerHTML = '<i class="fas fa-edit"></i> إعادة تسمية';
    renameBtn.className = 'btn secondary';
    renameBtn.onclick = () => {
        const newName = prompt('أدخل الاسم الجديد للمجلد:', folder.name);
        if (newName && newName.trim()) {
            renameSiteFolder(folder.id, newName);
            document.body.removeChild(modal);
        }
    };

    const deleteBtn = document.createElement('button');
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i> حذف المجلد';
    deleteBtn.className = 'btn secondary';
    deleteBtn.onclick = () => {
        if (confirm(`هل أنت متأكد من حذف مجلد "${folder.name}"؟\nسيتم نقل محتوياته إلى القائمة الرئيسية.`)) {
            deleteSiteFolder(folder.id);
            document.body.removeChild(modal);
        }
    };

    folderActions.appendChild(renameBtn);
    folderActions.appendChild(deleteBtn);

    header.appendChild(title);
    header.appendChild(folderActions);

    const sitesContainer = document.createElement('div');
    sitesContainer.style.marginBottom = '20px';

    if (folder.sites && folder.sites.length > 0) {
        folder.sites.forEach((site, siteIndex) => {
            const siteItem = document.createElement('div');
            siteItem.style.cssText = `
                display: flex;
                align-items: center;
                padding: 15px 18px;
                margin: 10px 0;
                background: #f8f4ff;
                border: 1px solid #e0cfff;
                border-radius: 10px;
                transition: all 0.3s ease;
                min-height: 60px;
                box-shadow: 0 3px 6px rgba(185, 124, 255, 0.15);
            `;

            siteItem.addEventListener('mouseenter', () => {
                siteItem.style.backgroundColor = '#f0e6ff';
                siteItem.style.borderColor = '#b97cff';
            });

            siteItem.addEventListener('mouseleave', () => {
                siteItem.style.backgroundColor = '#f8f4ff';
                siteItem.style.borderColor = '#e0cfff';
            });

            siteItem.innerHTML = `
                <div style="background: #b97cff; color: white; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 0.9em; margin-left: 10px; box-shadow: 0 2px 4px rgba(185, 124, 255, 0.3);">${siteIndex + 1}</div>
                <i class="fas fa-external-link-alt" style="color: #b97cff; margin-left: 12px; font-size: 1.1em;"></i>
                <span style="flex-grow: 1; color: #6c2eb7; font-weight: 600; font-size: 1.3em; line-height: 1.5; letter-spacing: 0.3px;">${site.name || site.url}</span>
                <div style="display: flex; gap: 4px; align-items: center;">
                    <button class="btn site-play-btn" onclick="openSiteInApp('${site.url}', '${(site.name || site.url).replace(/'/g, "\\'")}', '${folder.id}', ${siteIndex})" title="فتح داخل التطبيق" style="background: linear-gradient(135deg, #2196f3, #1976d2); color: white; padding: 8px 12px; border-radius: 8px; border: none; cursor: pointer; font-size: 1.1em; box-shadow: 0 3px 8px rgba(33, 150, 243, 0.3); transition: all 0.3s ease; margin-left: 8px;">
                        <i class="fas fa-desktop" style="margin-left: 4px;"></i>
                        فتح داخلياً
                    </button>
                    <button class="btn site-external-btn" onclick="window.open('${site.url}', '_blank')" title="فتح خارجياً" style="background: linear-gradient(135deg, #4caf50, #45a049); color: white; padding: 8px 12px; border-radius: 8px; border: none; cursor: pointer; font-size: 1.1em; box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3); transition: all 0.3s ease; margin-left: 8px;">
                        <i class="fas fa-external-link-alt" style="margin-left: 4px;"></i>
                        فتح خارجياً
                    </button>
                    <button class="btn secondary small" onclick="moveSiteInFolder('${folder.id}', ${siteIndex}, ${siteIndex - 1}); document.body.removeChild(this.closest('.modal')); showFolderContents('${folder.id}')" title="نقل لأعلى" ${siteIndex === 0 ? 'disabled' : ''}>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <button class="btn secondary small" onclick="moveSiteInFolder('${folder.id}', ${siteIndex}, ${siteIndex + 1}); document.body.removeChild(this.closest('.modal')); showFolderContents('${folder.id}')" title="نقل لأسفل" ${siteIndex === folder.sites.length - 1 ? 'disabled' : ''}>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <button class="btn secondary small" onclick="changeSitePosition('${folder.id}', ${siteIndex})" title="تغيير الترتيب">
                        <i class="fas fa-sort-numeric-down"></i>
                    </button>
                    <button class="btn secondary small" onclick="moveSiteFromFolder('${folder.id}', ${siteIndex}); document.body.removeChild(this.closest('.modal'))" title="نقل إلى القائمة الرئيسية">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                </div>
            `;

            sitesContainer.appendChild(siteItem);
        });
    } else {
        const emptyMessage = document.createElement('div');
        emptyMessage.style.cssText = `
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 40px 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        `;
        emptyMessage.textContent = 'المجلد فارغ. يمكنك إضافة مواقع إليه من القائمة الرئيسية.';
        sitesContainer.appendChild(emptyMessage);
    }

    const closeBtn = document.createElement('button');
    closeBtn.textContent = 'إغلاق';
    closeBtn.className = 'btn primary';
    closeBtn.style.width = '100%';
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modalContent.appendChild(header);
    modalContent.appendChild(sitesContainer);
    modalContent.appendChild(closeBtn);
    modal.appendChild(modalContent);

    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    document.body.appendChild(modal);
}

// فتح موقع داخل التطبيق من المجلد
function openSiteInApp(url, siteName, folderId, siteIndex) {
    // إنشاء كائن مؤقت للموقع
    const siteItem = {
        id: `folder-site-${folderId}-${siteIndex}`,
        name: siteName,
        href: url,
        img: '', // يمكن إضافة صورة افتراضية للمواقع
        category: 'movie-sites',
        isFolderSite: true,
        folderId: folderId,
        siteIndex: siteIndex
    };

    // فتح الموقع في مودال التشغيل
    openSitePlayModal(siteItem);
}

// مودال خاص لتشغيل المواقع مع إمكانية إضافتها كأفلام
function openSitePlayModal(siteItem) {
    console.log('بدء فتح مودال الموقع:', siteItem);

    const modal = document.getElementById('play-movie-modal');
    const titleElement = document.getElementById('play-movie-title');
    const playerFrame = document.getElementById('movie-player');
    const addToSubcategoryBtn = document.getElementById('add-to-subcategory-from-player');

    if (!modal || !titleElement || !playerFrame || !addToSubcategoryBtn) {
        console.error('عناصر المودال غير موجودة:', {
            modal: !!modal,
            titleElement: !!titleElement,
            playerFrame: !!playerFrame,
            addToSubcategoryBtn: !!addToSubcategoryBtn
        });
        showToast('خطأ: لا يمكن فتح مودال التشغيل', 'error');
        return;
    }

    titleElement.textContent = siteItem.name;

    // تحديث عنوان الصفحة
    const originalTitle = document.title;
    document.title = `تصفح: ${siteItem.name} - New Koktil-aflam v25`;

    console.log('تم تحديث عنوان المودال والصفحة');

    // تحديد رابط الموقع
    let siteUrl = siteItem.href || siteItem.url;
    console.log('الرابط الأصلي:', siteUrl);

    if (!siteUrl) {
        console.error('لا يوجد رابط للموقع');
        showToast('خطأ: لا يوجد رابط للموقع', 'error');
        return;
    }

    if (!siteUrl.startsWith('http://') && !siteUrl.startsWith('https://')) {
        siteUrl = 'https://' + siteUrl;
    }

    console.log('الرابط النهائي:', siteUrl);

    // تحميل الموقع في iframe
    playerFrame.src = siteUrl;
    console.log('تم تحديث iframe src إلى:', siteUrl);

    // إضافة خصائص لتحسين تتبع التغييرات
    playerFrame.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation');

    // تحسين تتبع التنقل داخل iframe
    const enhanceIframeTracking = () => {
        try {
            // محاولة إضافة مستمع للنقر داخل iframe
            if (playerFrame.contentDocument) {
                playerFrame.contentDocument.addEventListener('click', (e) => {
                    // تأخير قصير للسماح بتحميل الصفحة الجديدة
                    setTimeout(() => {
                        try {
                            const currentUrl = playerFrame.contentWindow.location.href;
                            const newTitle = extractTitleFromUrl(currentUrl);
                            document.title = `${newTitle} - New Koktil-aflam v25`;
                            console.log(`تم تحديث العنوان بعد النقر: ${newTitle}`);
                        } catch (e) {
                            // تجاهل أخطاء CORS
                        }
                    }, 1500);
                });
            }
        } catch (e) {
            // تجاهل أخطاء CORS
        }
    };

    // محاولة تحسين التتبع عند تحميل iframe
    playerFrame.addEventListener('load', () => {
        setTimeout(enhanceIframeTracking, 500);

        // محاولة تحديث العنوان فور التحميل
        setTimeout(() => {
            try {
                const currentUrl = playerFrame.contentWindow.location.href;
                if (currentUrl && currentUrl !== 'about:blank') {
                    const newTitle = extractTitleFromUrl(currentUrl);
                    document.title = `${newTitle} - New Koktil-aflam v25`;
                    window.currentIframeUrl = currentUrl;
                    window.currentIframeTitle = newTitle;
                    console.log(`تم تحديث العنوان عند التحميل: ${newTitle}`);
                }
            } catch (e) {
                // تجاهل أخطاء CORS
            }
        }, 1000);

        // تحديث دوري مكثف للعنوان والURL
        const intensiveUpdate = setInterval(() => {
            try {
                if (playerFrame.contentWindow && playerFrame.contentWindow.location) {
                    const currentUrl = playerFrame.contentWindow.location.href;
                    if (currentUrl && currentUrl !== 'about:blank' && currentUrl !== window.currentIframeUrl) {
                        window.currentIframeUrl = currentUrl;
                        const newTitle = extractTitleFromUrl(currentUrl);
                        document.title = `${newTitle} - New Koktil-aflam v25`;
                        window.currentIframeTitle = newTitle;
                        console.log(`تحديث مكثف - العنوان: ${newTitle}, URL: ${currentUrl}`);
                    }
                }

                // محاولة قراءة عنوان الصفحة أيضاً
                if (playerFrame.contentDocument && playerFrame.contentDocument.title) {
                    const docTitle = playerFrame.contentDocument.title;
                    if (docTitle && docTitle.trim() !== '' && docTitle !== window.currentIframeTitle) {
                        document.title = `${docTitle} - New Koktil-aflam v25`;
                        window.currentIframeTitle = docTitle;
                        console.log(`تحديث العنوان من document.title: ${docTitle}`);
                    }
                }
            } catch (e) {
                // تجاهل أخطاء CORS
            }
        }, 800); // كل 800ms

        // إيقاف التحديث المكثف عند إغلاق المودال
        const modal = document.getElementById('play-movie-modal');
        const originalClose = modal.querySelector('.close').onclick;
        modal.querySelector('.close').onclick = () => {
            clearInterval(intensiveUpdate);
            if (originalClose) originalClose();
        };
    });

    // إضافة تتبع لأحداث التنقل في iframe
    const addNavigationTracking = () => {
        try {
            if (playerFrame.contentWindow) {
                // تتبع أحداث beforeunload و unload
                playerFrame.contentWindow.addEventListener('beforeunload', () => {
                    setTimeout(() => {
                        try {
                            const currentUrl = playerFrame.contentWindow.location.href;
                            if (currentUrl && currentUrl !== 'about:blank') {
                                const newTitle = extractTitleFromUrl(currentUrl);
                                document.title = `${newTitle} - New Koktil-aflam v25`;
                                console.log(`تم تحديث العنوان عند التنقل: ${newTitle}`);
                            }
                        } catch (e) {
                            // تجاهل أخطاء CORS
                        }
                    }, 500);
                });

                // تتبع تغيير hash
                playerFrame.contentWindow.addEventListener('hashchange', () => {
                    setTimeout(() => {
                        try {
                            const currentUrl = playerFrame.contentWindow.location.href;
                            const newTitle = extractTitleFromUrl(currentUrl);
                            document.title = `${newTitle} - New Koktil-aflam v25`;
                            console.log(`تم تحديث العنوان عند تغيير hash: ${newTitle}`);
                        } catch (e) {
                            // تجاهل أخطاء CORS
                        }
                    }, 200);
                });
            }
        } catch (e) {
            // تجاهل أخطاء CORS
        }
    };

    // محاولة إضافة تتبع التنقل
    playerFrame.addEventListener('load', () => {
        setTimeout(addNavigationTracking, 1000);
    });

    // إضافة زر إضافة كفيلم بجانب زر إضافة إلى قسم فرعي
    const modalHeaderActions = document.querySelector('.modal-header-actions');

    // إزالة زر إضافة كفيلم السابق إن وجد
    const existingAddAsMovieBtn = document.getElementById('add-as-movie-btn');
    if (existingAddAsMovieBtn) {
        existingAddAsMovieBtn.remove();
    }

    // إنشاء زر إضافة كفيلم
    const addAsMovieBtn = document.createElement('button');
    addAsMovieBtn.id = 'add-as-movie-btn';
    addAsMovieBtn.className = 'btn secondary';
    addAsMovieBtn.title = 'إضافة كفيلم إلى قسم';
    addAsMovieBtn.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة كفيلم';
    addAsMovieBtn.style.cssText = `
        background: linear-gradient(135deg, #9c27b0, #7b1fa2);
        color: white;
        border: none;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(156, 39, 176, 0.2);
        margin-left: 8px;
    `;

    // إضافة تأثيرات hover
    addAsMovieBtn.addEventListener('mouseenter', () => {
        addAsMovieBtn.style.background = 'linear-gradient(135deg, #7b1fa2, #4a148c)';
        addAsMovieBtn.style.transform = 'translateY(-2px)';
        addAsMovieBtn.style.boxShadow = '0 4px 12px rgba(156, 39, 176, 0.4)';
    });

    addAsMovieBtn.addEventListener('mouseleave', () => {
        addAsMovieBtn.style.background = 'linear-gradient(135deg, #9c27b0, #7b1fa2)';
        addAsMovieBtn.style.transform = 'translateY(0)';
        addAsMovieBtn.style.boxShadow = '0 2px 8px rgba(156, 39, 176, 0.2)';
    });

    // إضافة وظيفة الزر
    addAsMovieBtn.onclick = (e) => {
        e.stopPropagation();
        openAddAsMovieModal(siteItem);
    };

    // إدراج الزر قبل زر إضافة إلى قسم فرعي
    modalHeaderActions.insertBefore(addAsMovieBtn, addToSubcategoryBtn);

    // إضافة زر تحديث العنوان يدوياً
    const refreshTitleBtn = document.createElement('button');
    refreshTitleBtn.className = 'btn secondary';
    refreshTitleBtn.title = 'تحديث العنوان يدوياً';
    refreshTitleBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
    refreshTitleBtn.style.cssText = `
        background: linear-gradient(135deg, #607d8b, #455a64);
        color: white;
        border: none;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(96, 125, 139, 0.2);
        margin-left: 8px;
        padding: 8px 12px;
    `;

    refreshTitleBtn.onclick = (e) => {
        e.stopPropagation();

        // حل بديل: طلب من المستخدم إدخال العنوان يدوياً
        const currentTitle = window.currentIframeTitle || 'عنوان الصفحة';
        const newTitle = prompt(`بسبب قيود الأمان، يرجى إدخال عنوان الصفحة الحالية:\n\n(يمكنك نسخه من شريط عنوان المتصفح داخل الإطار)`, currentTitle);

        if (newTitle && newTitle.trim() !== '' && newTitle !== currentTitle) {
            document.title = `${newTitle.trim()} - New Koktil-aflam v25`;
            window.currentIframeTitle = newTitle.trim();
            showToast(`تم تحديث العنوان: ${newTitle.trim()}`, 'success');
        } else if (newTitle === null) {
            // المستخدم ألغى العملية
            return;
        } else {
            showToast('لم يتم تغيير العنوان', 'info');
        }
    };

    // إضافة زر تحديث URL يدوياً
    const updateUrlBtn = document.createElement('button');
    updateUrlBtn.className = 'btn secondary';
    updateUrlBtn.title = 'تحديث رابط الصفحة الحالية';
    updateUrlBtn.innerHTML = '<i class="fas fa-link"></i>';
    updateUrlBtn.style.cssText = `
        background: linear-gradient(135deg, #2196f3, #1976d2);
        color: white;
        border: none;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
        margin-left: 8px;
        padding: 8px 12px;
    `;

    updateUrlBtn.onclick = (e) => {
        e.stopPropagation();

        // طلب من المستخدم إدخال URL الحالي
        const currentUrl = window.currentIframeUrl || '';
        const newUrl = prompt(`يرجى إدخال رابط الصفحة الحالية:\n\n(انسخ الرابط من شريط العنوان داخل الإطار)`, currentUrl);

        if (newUrl && newUrl.trim() !== '' && newUrl !== currentUrl) {
            window.currentIframeUrl = newUrl.trim();
            // محاولة استخراج عنوان من URL الجديد
            const extractedTitle = extractTitleFromUrl(newUrl.trim());
            if (extractedTitle && extractedTitle !== 'صفحة جديدة') {
                document.title = `${extractedTitle} - New Koktil-aflam v25`;
                window.currentIframeTitle = extractedTitle;
                showToast(`تم تحديث الرابط والعنوان: ${extractedTitle}`, 'success');
            } else {
                showToast(`تم تحديث الرابط: ${newUrl.trim()}`, 'success');
            }
        } else if (newUrl === null) {
            return;
        } else {
            showToast('لم يتم تغيير الرابط', 'info');
        }
    };

    modalHeaderActions.insertBefore(updateUrlBtn, refreshTitleBtn);
    modalHeaderActions.insertBefore(refreshTitleBtn, addAsMovieBtn);

    // إعداد زر إضافة إلى قسم فرعي (للمواقع المحفوظة كأفلام)
    addToSubcategoryBtn.onclick = (e) => {
        e.stopPropagation();
        // تحويل الموقع إلى فيلم مؤقت لإضافته إلى قسم فرعي
        const tempMovie = {
            id: siteItem.id,
            name: siteItem.name,
            href: siteItem.href,
            img: siteItem.img || '',
            category: 'all', // سيتم تحديد القسم عند الإضافة
            subCategories: []
        };
        openAddToSubcategoryModal(tempMovie);
    };

    // إضافة اختصارات لوحة المفاتيح
    const keyboardHandler = (e) => {
        if (e.ctrlKey && e.key === 'u') {
            e.preventDefault();
            updateUrlBtn.click();
        } else if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshTitleBtn.click();
        } else if (e.key === 'F2') {
            e.preventDefault();
            refreshTitleBtn.click();
        }
    };

    document.addEventListener('keydown', keyboardHandler);

    // وظيفة الإغلاق
    const closeModal = () => {
        modal.classList.remove('show');
        playerFrame.src = '';
        // إزالة الأزرار المضافة
        const addAsMovieBtn = document.getElementById('add-as-movie-btn');
        if (addAsMovieBtn) {
            addAsMovieBtn.remove();
        }
        const refreshTitleBtn = modalHeaderActions.querySelector('.btn.secondary[title="تحديث العنوان يدوياً"]');
        if (refreshTitleBtn) {
            refreshTitleBtn.remove();
        }
        const updateUrlBtn = modalHeaderActions.querySelector('.btn.secondary[title="تحديث رابط الصفحة الحالية"]');
        if (updateUrlBtn) {
            updateUrlBtn.remove();
        }
        // إزالة مستمع الأحداث
        document.removeEventListener('keydown', keyboardHandler);
        // استعادة العنوان الأصلي
        document.title = originalTitle;
    };

    // عرض المودال
    modal.classList.add('show');

    // عرض رسالة توضيحية للمستخدم
    setTimeout(() => {
        showToast('💡 نصيحة: استخدم أزرار 🔗 و 🔄 أو Ctrl+U / Ctrl+R لتحديث الرابط والعنوان', 'info', 7000);
    }, 2000);

    // زر الإغلاق
    modal.querySelector('.close').onclick = closeModal;

    // إغلاق المودال عند النقر خارجه
    modal.onclick = (e) => {
        if (e.target === modal) {
            closeModal();
        }
    };

    // تتبع تغيير عنوان الصفحة عند التنقل داخل iframe
    setupPageTitleTracking(playerFrame, siteItem, originalTitle);
}

// مودال إضافة الموقع كفيلم
function openAddAsMovieModal(siteItem) {
    // الحصول على URL والعنوان الحاليين
    const currentUrl = window.currentIframeUrl || siteItem.href;
    const currentTitle = window.currentIframeTitle || siteItem.name;

    console.log(`فتح مودال إضافة فيلم - URL: ${currentUrl}, العنوان: ${currentTitle}`);

    // إنشاء المودال
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.style.zIndex = '10001'; // أعلى من مودال التشغيل

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.maxWidth = '500px';

    modalContent.innerHTML = `
        <div class="modal-header">
            <h3><i class="fas fa-plus-circle" style="color: #9c27b0; margin-left: 8px;"></i>إضافة كفيلم</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="movie-name-input">اسم الفيلم:</label>
                <input type="text" id="movie-name-input" value="${currentTitle.replace(/"/g, '&quot;')}" class="form-control">
            </div>
            <div class="form-group">
                <label for="movie-url-input">رابط الفيلم:</label>
                <input type="text" id="movie-url-input" value="${currentUrl.replace(/"/g, '&quot;')}" class="form-control">
            </div>
            <div class="form-group">
                <label for="movie-image-input">رابط الصورة (اختياري):</label>
                <input type="text" id="movie-image-input" value="${siteItem.img || ''}" class="form-control" placeholder="https://example.com/image.jpg">
            </div>
            <div class="form-group">
                <label for="movie-category-select">القسم:</label>
                <select id="movie-category-select" class="form-control">
                    <option value="all">جميع الأفلام والمسلسلات</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="movie-is-series-checkbox"> مسلسل
                </label>
            </div>
        </div>
        <div class="modal-footer">
            <button id="save-as-movie-btn" class="btn primary">
                <i class="fas fa-save"></i> حفظ كفيلم
            </button>
            <button id="cancel-add-movie-btn" class="btn secondary">إلغاء</button>
        </div>
    `;

    // ملء قائمة الأقسام
    const categorySelect = modalContent.querySelector('#movie-category-select');

    // إضافة الأقسام الرئيسية
    appState.categories.main.forEach(category => {
        if (category.id !== 'movie-sites' && category.id !== 'all') {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        }
    });

    // إضافة الأقسام الخاصة
    appState.categories.special.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name + ' (خاص)';
        categorySelect.appendChild(option);
    });

    modal.appendChild(modalContent);

    // إضافة مستمعي الأحداث
    const closeBtn = modalContent.querySelector('.close');
    const cancelBtn = modalContent.querySelector('#cancel-add-movie-btn');
    const saveBtn = modalContent.querySelector('#save-as-movie-btn');
    const movieNameInput = modalContent.querySelector('#movie-name-input');

    // تظليل اسم الفيلم تمهيداً لتغييره
    setTimeout(() => {
        movieNameInput.focus();
        movieNameInput.select(); // تظليل النص بالكامل
    }, 100);

    const closeModal = () => {
        document.body.removeChild(modal);
    };

    closeBtn.onclick = closeModal;
    cancelBtn.onclick = closeModal;

    saveBtn.onclick = () => {
        const name = modalContent.querySelector('#movie-name-input').value.trim();
        const url = modalContent.querySelector('#movie-url-input').value.trim();
        const image = modalContent.querySelector('#movie-image-input').value.trim();
        const category = modalContent.querySelector('#movie-category-select').value;
        const isSeries = modalContent.querySelector('#movie-is-series-checkbox').checked;

        if (!name || !url) {
            showToast('يرجى ملء الحقول المطلوبة', 'warning');
            return;
        }

        // إنشاء الفيلم/المسلسل الجديد
        const newItem = {
            id: generateUniqueId(),
            name: name,
            href: url,
            img: image || '',
            category: category,
            subCategories: [],
            hidden: false
        };

        // إضافة إلى القائمة المناسبة
        if (isSeries) {
            appState.series.push(newItem);
        } else {
            appState.movies.push(newItem);
        }

        // حفظ البيانات وتحديث الواجهة
        saveAppData();
        updateCategoriesCounts();
        renderCategories();

        // تحديث العرض الحالي إذا كان في نفس القسم
        if (appState.currentCategory === category || appState.currentCategory === 'all') {
            displayMovies(appState.currentCategory, appState.currentPage);
        }

        showToast(`تم إضافة ${isSeries ? 'المسلسل' : 'الفيلم'} "${name}" بنجاح`, 'success');
        closeModal();
    };

    // إغلاق عند النقر خارج المودال
    modal.onclick = (e) => {
        if (e.target === modal) {
            closeModal();
        }
    };

    document.body.appendChild(modal);
}

// إضافة مستمعي الأحداث لبطاقات الأفلام
function addMovieCardEventListeners() {
    // زر التشغيل
    document.querySelectorAll('.movie-play-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const movieId = button.dataset.id;
            const movieHref = button.dataset.href;
            const item = findMovieById(movieId);

            if (item) {
                openPlayModal(item);
            }
        });
    });

    // زر التمييز (الإضافة إلى قسم فرعي)
    document.querySelectorAll('.movie-favorite-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const movieId = button.dataset.id;
            const item = findMovieById(movieId);

            if (item) {
                openAddToSubcategoryModal(item);
                button.classList.add('marked');
            }
        });
    });

    // زر الحذف من القسم الفرعي
    document.querySelectorAll('.movie-remove-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const movieId = button.dataset.id;
            const subcategoryId = button.dataset.subcategory;
            const item = findMovieById(movieId);

            if (item && subcategoryId) {
                removeFromSubcategory(item, subcategoryId);
            }
        });
    });

    // زر التعديل
    document.querySelectorAll('.movie-edit-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const movieId = button.dataset.id;
            const item = findMovieById(movieId);

            if (item) {
                openEditModal(item);
            }
        });
    });

    // زر حذف الفيلم
    document.querySelectorAll('.movie-delete-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const movieId = button.dataset.id;
            const item = findMovieById(movieId);

            if (item) {
                // عرض مودال التأكيد
                const modal = document.getElementById('confirm-modal');
                const messageElement = document.getElementById('confirm-message');

                messageElement.textContent = `هل أنت متأكد من رغبتك في حذف "${item.name}"؟`;

                // عرض المودال
                modal.classList.add('show');

                // زر نعم
                document.getElementById('confirm-yes').onclick = () => {
                    deleteMovie(movieId);
                    modal.classList.remove('show');
                };

                // زر لا
                document.getElementById('confirm-no').onclick = () => {
                    modal.classList.remove('show');
                };

                // إغلاق المودال عند النقر على X
                modal.querySelector('.close').onclick = () => {
                    modal.classList.remove('show');
                };

                // إغلاق المودال عند النقر خارجه
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        modal.classList.remove('show');
                    }
                };
            }
        });
    });
}

// البحث عن فيلم حسب المعرّف
function findMovieById(id) {
    let item = appState.movies.find(movie => movie.id === id);
    if (!item) {
        item = appState.series.find(series => series.id === id);
    }
    if (!item && id && id.startsWith('site-')) {
        const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
        if (movieSitesCat && Array.isArray(movieSitesCat.shortcuts)) {
            const idx = parseInt(id.replace('site-', ''));
            const shortcut = movieSitesCat.shortcuts[idx];
            if (shortcut) {
                return {
                    ...shortcut,
                    id: id,
                    category: 'movie-sites',
                    isSiteShortcut: true,
                    _shortcutIndex: idx
                };
            }
        }
    }
    return item;
}

// فتح مودال تشغيل الفيلم
function openPlayModal(item) {
    if (appState.openMoviesExternally) {
        // فتح الفيلم في متصفح خارجي
        window.open(item.href, '_blank');
        // تحديث عنوان الصفحة مؤقتاً لإظهار أن الفيلم تم فتحه
        const originalTitle = document.title;
        document.title = `فتح خارجي: ${item.name} - New Koktil-aflam v25`;
        // استعادة العنوان الأصلي بعد 3 ثوانٍ
        setTimeout(() => {
            document.title = originalTitle;
        }, 3000);
        return;
    }

    const modal = document.getElementById('play-movie-modal');
    const titleElement = document.getElementById('play-movie-title');
    const playerFrame = document.getElementById('movie-player');
    const addToSubcategoryBtn = document.getElementById('add-to-subcategory-from-player');

    titleElement.textContent = item.name;

    // تحديث عنوان الصفحة
    const originalTitle = document.title;
    document.title = `تشغيل: ${item.name} - New Koktil-aflam v25`;

    // تجهيز iframe لتشغيل الفيديو
    if (item.href) {
        if (item.href.includes('youtube.com') || item.href.includes('youtu.be')) {
            // تحويل روابط اليوتيوب العادية إلى روابط embed
            let youtubeId = '';
            if (item.href.includes('youtu.be/')) {
                youtubeId = item.href.split('youtu.be/')[1];
            } else if (item.href.includes('watch?v=')) {
                youtubeId = item.href.split('watch?v=')[1].split('&')[0];
            }

            if (youtubeId) {
                playerFrame.src = `https://www.youtube.com/embed/${youtubeId}`;
            } else {
                playerFrame.src = item.href;
            }
        } else {
            playerFrame.src = item.href;
        }
    } else {
        playerFrame.src = '';
    }

    // إعداد زر إضافة إلى قسم فرعي
    addToSubcategoryBtn.onclick = (e) => {
        e.stopPropagation();
        openAddToSubcategoryModal(item);
    };

    // عرض المودال
    modal.classList.add('show');

    // وظيفة الإغلاق
    const closeModal = () => {
        modal.classList.remove('show');
        playerFrame.src = '';
        // استعادة العنوان الأصلي
        document.title = originalTitle;
    };

    // زر الإغلاق
    modal.querySelector('.close').onclick = closeModal;

    // إغلاق المودال عند النقر خارجه
    modal.onclick = (e) => {
        if (e.target === modal) {
            closeModal();
        }
    };

    // تتبع تغيير عنوان الصفحة عند التنقل داخل iframe
    setupPageTitleTracking(playerFrame, item, originalTitle);
}

// إعداد تتبع تغيير عنوان الصفحة
function setupPageTitleTracking(iframe, item, originalTitle) {
    let titleUpdateInterval;
    let lastKnownUrl = iframe.src;
    let currentPageUrl = iframe.src; // متغير لحفظ URL الحالي

    // تحديث URL الحالي للاستخدام في "إضافة كفيلم"
    window.currentIframeUrl = iframe.src;
    window.currentIframeTitle = item.name;

    // محاولة تتبع تغيير عنوان الصفحة داخل iframe
    const trackTitleChanges = () => {
        try {
            // محاولة الوصول إلى عنوان الصفحة داخل iframe
            if (iframe.contentDocument && iframe.contentDocument.title) {
                const iframeTitle = iframe.contentDocument.title;
                if (iframeTitle && iframeTitle !== item.name && iframeTitle.trim() !== '') {
                    document.title = `${iframeTitle} - New Koktil-aflam v25`;
                    window.currentIframeTitle = iframeTitle;
                }
            }
        } catch (e) {
            // تجاهل أخطاء CORS
        }
    };

    // تتبع تغيير URL في iframe (محدود بسبب CORS)
    const trackUrlChanges = () => {
        try {
            if (iframe.contentWindow && iframe.contentWindow.location) {
                const currentUrl = iframe.contentWindow.location.href;
                if (currentUrl !== lastKnownUrl && currentUrl !== 'about:blank') {
                    lastKnownUrl = currentUrl;
                    currentPageUrl = currentUrl;
                    window.currentIframeUrl = currentUrl;

                    // استخراج عنوان أفضل من URL الجديد
                    const newTitle = extractTitleFromUrl(currentUrl);
                    document.title = `${newTitle} - New Koktil-aflam v25`;
                    window.currentIframeTitle = newTitle;
                    console.log(`تم تحديث العنوان إلى: ${newTitle}`);
                }
            }
        } catch (e) {
            // تجاهل أخطاء CORS - هذا طبيعي للمواقع الخارجية
        }
    };

    // محاولة تتبع التغييرات باستخدام postMessage
    const setupPostMessageTracking = () => {
        // إضافة مستمع لرسائل من iframe
        const messageHandler = (event) => {
            // التحقق من أن الرسالة من iframe الصحيح
            if (event.source === iframe.contentWindow) {
                if (event.data && event.data.type === 'urlChange') {
                    const newUrl = event.data.url;
                    if (newUrl && newUrl !== lastKnownUrl) {
                        lastKnownUrl = newUrl;
                        const newTitle = extractTitleFromUrl(newUrl);
                        document.title = `${newTitle} - New Koktil-aflam v25`;
                        console.log(`تم تحديث العنوان من postMessage: ${newTitle}`);
                    }
                } else if (event.data && event.data.type === 'titleChange') {
                    const newTitle = event.data.title;
                    if (newTitle && newTitle.trim() !== '') {
                        document.title = `${newTitle} - New Koktil-aflam v25`;
                    }
                }
            }
        };

        window.addEventListener('message', messageHandler);

        // إرسال script لـ iframe لتتبع التغييرات (إذا كان ممكناً)
        iframe.onload = () => {
            try {
                if (iframe.contentDocument) {
                    // محاولة حقن script لتتبع التغييرات
                    const script = iframe.contentDocument.createElement('script');
                    script.textContent = `
                        (function() {
                            let lastUrl = location.href;
                            let lastTitle = document.title;

                            // تتبع تغيير URL
                            setInterval(function() {
                                if (location.href !== lastUrl) {
                                    lastUrl = location.href;
                                    parent.postMessage({type: 'urlChange', url: lastUrl}, '*');
                                }
                                if (document.title !== lastTitle) {
                                    lastTitle = document.title;
                                    parent.postMessage({type: 'titleChange', title: lastTitle}, '*');
                                }
                            }, 1000);

                            // تتبع تغيير التاريخ
                            const originalPushState = history.pushState;
                            const originalReplaceState = history.replaceState;

                            history.pushState = function() {
                                originalPushState.apply(history, arguments);
                                setTimeout(() => {
                                    parent.postMessage({type: 'urlChange', url: location.href}, '*');
                                }, 100);
                            };

                            history.replaceState = function() {
                                originalReplaceState.apply(history, arguments);
                                setTimeout(() => {
                                    parent.postMessage({type: 'urlChange', url: location.href}, '*');
                                }, 100);
                            };

                            // تتبع أحداث popstate
                            window.addEventListener('popstate', function() {
                                setTimeout(() => {
                                    parent.postMessage({type: 'urlChange', url: location.href}, '*');
                                }, 100);
                            });
                        })();
                    `;
                    iframe.contentDocument.head.appendChild(script);
                }
            } catch (e) {
                // فشل في حقن الـ script بسبب CORS
                console.log('لا يمكن حقن script لتتبع التغييرات بسبب قيود CORS');
            }

            // محاولة تحديث العنوان عند تحميل صفحة جديدة
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 1000);
        };

        return messageHandler;
    };

    // إضافة تتبع بديل باستخدام focus events
    const addFocusTracking = () => {
        // تتبع عندما يحصل iframe على focus
        iframe.addEventListener('focus', () => {
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 500);
        });

        // تتبع عندما يفقد iframe focus (قد يعني تغيير الصفحة)
        iframe.addEventListener('blur', () => {
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 1000);
        });

        // تتبع عند النقر على iframe
        iframe.addEventListener('mouseenter', () => {
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 500);
        });

        // تتبع أحداث الماوس داخل iframe
        iframe.addEventListener('mouseover', () => {
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 300);
        });

        // تتبع عند النقر (قد يعني تغيير الصفحة)
        iframe.addEventListener('click', () => {
            // تأخير أطول للسماح بتحميل الصفحة الجديدة
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 1500);

            // تحديث إضافي بعد تأخير أطول
            setTimeout(() => {
                trackTitleChanges();
                trackUrlChanges();
            }, 3000);
        });
    };

    // تتبع أكثر تكراراً للتغييرات
    const intensiveTracking = () => {
        // فحص كل ثانية واحدة بدلاً من كل ثانيتين
        setInterval(() => {
            trackTitleChanges();
            trackUrlChanges();
        }, 1000);

        // فحص إضافي كل 500ms للعنوان فقط
        setInterval(() => {
            trackTitleChanges();
        }, 500);
    };

    // بدء تتبع التغييرات
    const messageHandler = setupPostMessageTracking();
    addFocusTracking();
    intensiveTracking();

    // الاحتفاظ بالتتبع الأساسي كنسخة احتياطية
    titleUpdateInterval = setInterval(trackTitleChanges, 2000);
    urlCheckInterval = setInterval(trackUrlChanges, 1500);

    // إيقاف التتبع عند إغلاق المودال
    const modal = document.getElementById('play-movie-modal');
    const originalCloseHandler = modal.querySelector('.close').onclick;

    const cleanup = () => {
        if (titleUpdateInterval) {
            clearInterval(titleUpdateInterval);
        }
        if (urlCheckInterval) {
            clearInterval(urlCheckInterval);
        }
        if (messageHandler) {
            window.removeEventListener('message', messageHandler);
        }
        document.title = originalTitle;
    };

    modal.querySelector('.close').onclick = () => {
        cleanup();
        if (originalCloseHandler) originalCloseHandler();
    };

    // تنظيف عند إغلاق النافذة
    window.addEventListener('beforeunload', cleanup);

    // إضافة تتبع إضافي باستخدام MutationObserver لتتبع تغييرات src
    const srcObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                const newSrc = iframe.getAttribute('src');
                if (newSrc && newSrc !== lastKnownUrl && newSrc !== 'about:blank') {
                    lastKnownUrl = newSrc;
                    const newTitle = extractTitleFromUrl(newSrc);
                    document.title = `${newTitle} - New Koktil-aflam v25`;
                    console.log(`تم تحديث العنوان من MutationObserver: ${newTitle}`);
                }
            }
        });
    });

    // بدء مراقبة تغييرات src
    srcObserver.observe(iframe, {
        attributes: true,
        attributeFilter: ['src']
    });

    // إضافة srcObserver للتنظيف
    const originalCleanup = cleanup;
    cleanup = () => {
        srcObserver.disconnect();
        originalCleanup();
    };
}

// وظيفة مساعدة لاستخراج عنوان أفضل من URL
function extractTitleFromUrl(url) {
    try {
        const urlObj = new URL(url);
        const hostname = urlObj.hostname.replace('www.', '');
        const pathname = urlObj.pathname;
        const searchParams = urlObj.searchParams;

        // محاولة استخراج عنوان من معاملات URL
        const titleParams = ['title', 'q', 'search', 'query', 'name'];
        for (const param of titleParams) {
            const value = searchParams.get(param);
            if (value && value.length > 0 && value.length < 100) {
                return decodeURIComponent(value).replace(/[+_-]/g, ' ').trim();
            }
        }

        // استخراج من المسار
        if (pathname && pathname !== '/' && pathname !== '') {
            const pathParts = pathname.split('/').filter(part => part && part.length > 0);
            if (pathParts.length > 0) {
                const lastPart = pathParts[pathParts.length - 1];
                const cleanPart = lastPart
                    .replace(/\.(html|php|asp|jsp|htm)$/i, '')
                    .replace(/[-_]/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase())
                    .trim();

                if (cleanPart.length > 0 && cleanPart.length < 50) {
                    return `${cleanPart} - ${hostname}`;
                }
            }
        }

        // إرجاع اسم الموقع فقط
        return hostname;
    } catch (e) {
        return 'صفحة جديدة';
    }
}

// فتح مودال إضافة إلى قسم فرعي
function openAddToSubcategoryModal(item) {
    const modal = document.getElementById('add-to-subcategory-modal');
    const nameElement = document.getElementById('subcategory-movie-name');
    const idElement = document.getElementById('subcategory-movie-id');

    nameElement.textContent = item.name;
    idElement.value = item.id;

    // تحديث خيارات الأقسام الفرعية بناءً على القسم الحالي
    updateSubcategoryOptions(item);

    // عرض المودال
    modal.classList.add('show');

    // إضافة مستمع أحداث لأزرار الاختيار
    document.querySelectorAll('.subcategory-option').forEach(button => {
        button.addEventListener('click', function() {
            const subcategoryId = this.dataset.category;
            addToSubcategory(item, subcategoryId);
            modal.classList.remove('show');
        });
    });

    // زر إلغاء
    document.getElementById('cancel-subcategory-btn').onclick = () => {
        modal.classList.remove('show');
    };

    // إغلاق المودال عند النقر على X
    modal.querySelector('.close').onclick = () => {
        modal.classList.remove('show');
    };

    // إغلاق المودال عند النقر خارجه
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    };
}

// تحديث خيارات الأقسام الفرعية بناءً على القسم الحالي
function updateSubcategoryOptions(item) {
    const optionsContainer = document.getElementById('subcategory-options-container');
    if (!optionsContainer) return;

    // تفريغ الحاوية
    optionsContainer.innerHTML = '';

    // تحديد القسم الحالي
    const currentCategory = item.category;
    const isSpecialCategory = appState.categories.special.some(cat => cat.id === currentCategory);
    const isRCategory = currentCategory === 'r1' || currentCategory === 'r2';
    const isSCategory = currentCategory === 's1' || currentCategory === 's2' || currentCategory === 's3' || currentCategory === 's-sites';

    // إنشاء الخيارات المناسبة
    let options = [];

    // إضافة الأقسام الفرعية العادية
    options.push({ id: 'selected1', name: 'أفلام مختارة 1' });
    options.push({ id: 'selected2', name: 'أفلام مختارة 2' });
    options.push({ id: 'favorite1', name: 'مفضلة أفلام 1' });
    options.push({ id: 'favorite2', name: 'مفضلة أفلام 2' });

    // إضافة الأقسام الفرعية الخاصة إذا كان قسمًا خاصًا أو R أو S
    if (isRCategory || isSCategory) {
        options.push({ id: 'selected-rs1', name: 'أفلام مختارة R+S1' });
        options.push({ id: 'selected-rs2', name: 'أفلام مختارة R+S2' });
    }

    // إنشاء الأزرار
    options.forEach(option => {
        const button = document.createElement('button');
        button.textContent = option.name;
        button.className = 'subcategory-option';
        button.dataset.category = option.id;

        // التحقق مما إذا كان الفيلم مضافًا بالفعل إلى هذا القسم الفرعي
        if (item.subCategories && item.subCategories.includes(option.id)) {
            button.classList.add('active');
        }

        optionsContainer.appendChild(button);
    });
}

// إضافة عنصر إلى قسم فرعي
function addToSubcategory(item, subcategoryId) {
    // التأكد من وجود مصفوفة subCategories
    if (!item.subCategories) {
        item.subCategories = [];
    }

    // التحقق من عدم وجود القسم الفرعي بالفعل
    if (!item.subCategories.includes(subcategoryId)) {
        // إضافة القسم الفرعي
        item.subCategories.push(subcategoryId);

        // حفظ موضع التمرير الحالي
        const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // حفظ البيانات وتحديث الواجهة
        saveAppData();
        updateCategoriesCounts();
        renderCategories();
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

        // استعادة موضع التمرير بعد تحديث الواجهة
        setTimeout(() => {
            window.scrollTo(0, currentScrollPosition);
        }, 50);

        showToast(`تمت إضافة "${item.name}" إلى القسم الفرعي بنجاح`, 'success');
    } else {
        showToast(`العنصر موجود بالفعل في هذا القسم الفرعي`, 'info');
    }
}

// إزالة عنصر من قسم فرعي
function removeFromSubcategory(item, subcategoryId) {
    if (item.subCategories && item.subCategories.includes(subcategoryId)) {
        // عرض مودال التأكيد
        const modal = document.getElementById('confirm-modal');
        const messageElement = document.getElementById('confirm-message');

        messageElement.textContent = `هل أنت متأكد من رغبتك في إزالة "${item.name}" من هذا القسم الفرعي؟`;

        // عرض المودال
        modal.classList.add('show');

        // زر نعم
        document.getElementById('confirm-yes').onclick = () => {
            // إزالة القسم الفرعي
            item.subCategories = item.subCategories.filter(id => id !== subcategoryId);

            // حفظ موضع التمرير الحالي
            const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // حفظ البيانات وتحديث الواجهة
            saveAppData();
            updateCategoriesCounts();
            renderCategories();
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

            // استعادة موضع التمرير بعد تحديث الواجهة
            setTimeout(() => {
                window.scrollTo(0, currentScrollPosition);
            }, 50);

            modal.classList.remove('show');
            showToast(`تمت إزالة العنصر من القسم الفرعي بنجاح`, 'success');
        };

        // زر لا
        document.getElementById('confirm-no').onclick = () => {
            modal.classList.remove('show');
        };

        // إغلاق المودال عند النقر على X
        modal.querySelector('.close').onclick = () => {
            modal.classList.remove('show');
        };

        // إغلاق المودال عند النقر خارجه
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
            }
        };
    }
}

// حذف فيلم
function deleteMovie(movieId) {
    const item = findMovieById(movieId);

    if (!item) {
        showToast('لم يتم العثور على الفيلم', 'error');
        return;
    }

    // التعامل مع اختصارات المواقع
    if (item.isSiteShortcut && typeof item._shortcutIndex !== 'undefined') {
        const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
        if (movieSitesCat && Array.isArray(movieSitesCat.shortcuts)) {
            movieSitesCat.shortcuts.splice(item._shortcutIndex, 1);
        }
    } else if (item.category === 'series') {
        appState.series = appState.series.filter(series => series.id !== movieId);
    } else {
        appState.movies = appState.movies.filter(movie => movie.id !== movieId);
    }

    // حفظ موضع التمرير الحالي
    const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // حفظ البيانات وتحديث الواجهة
    saveAppData();
    updateCategoriesCounts();
    renderCategories();
    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

    // استعادة موضع التمرير بعد تحديث الواجهة
    setTimeout(() => {
        window.scrollTo(0, currentScrollPosition);
    }, 50);

    showToast(`تم حذف "${item.name}" بنجاح`, 'success');
}

// عرض رسائل التوست
function showToast(message, type = 'info') {
    console.log(`عرض رسالة توست: ${type} - ${message}`);

    // إنشاء عنصر التوست
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    // تحويل الأسطر الجديدة إلى <br> لدعم الرسائل متعددة الأسطر
    const formattedMessage = message.replace(/\n/g, '<br>');
    toast.innerHTML = formattedMessage;

    // إضافة أنماط إضافية للتوست
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
        word-wrap: break-word;
        font-size: 14px;
        line-height: 1.4;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    // إضافة إلى الصفحة
    document.body.appendChild(toast);

    // إظهار التوست
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء التوست بعد فترة (أطول للرسائل الطويلة)
    const hideDelay = message.length > 100 ? 5000 : 3000;
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, hideDelay);
}

// حفظ سرعة الاستيراد الافتراضية
function saveDefaultImportSpeed(speed) {
    localStorage.setItem(STORAGE_KEYS.DEFAULT_IMPORT_SPEED, speed);
    showToast(`تم تعيين سرعة الاستيراد الافتراضية إلى ${speed}`, 'success');
}

// إضافة مستمعي الأحداث للتطبيق
function setupEventListeners() {

    // زر حذف كل أفلام الأقسام الفرعية
    const deleteSubMoviesBtn = document.getElementById('delete-subcategories-movies-btn');
    if (deleteSubMoviesBtn) {
        deleteSubMoviesBtn.addEventListener('click', function() {
            const type = document.getElementById('subcategory-type-select').value;
            let removedCount = 0;
            if (type === 'sub') {
                // حذف كل الأفلام التي تحتوي على أي subCategories من الأقسام العامة الفرعية
                const subIds = (appState.categories.sub || []).map(cat => cat.id);
                const before = appState.movies.length;
                appState.movies = appState.movies.filter(movie => {
                    if (!movie.subCategories) return true;
                    return !movie.subCategories.some(subId => subIds.includes(subId));
                });
                removedCount = before - appState.movies.length;
            } else if (type === 'specialSub') {
                // حذف كل الأفلام التي تحتوي على أي specialSubCategories من الأقسام الخاصة الفرعية
                const subIds = (appState.categories.specialSub || []).map(cat => cat.id);
                const before = appState.movies.length;
                appState.movies = appState.movies.filter(movie => {
                    if (!movie.specialSubCategories) return true;
                    return !movie.specialSubCategories.some(subId => subIds.includes(subId));
                });
                removedCount = before - appState.movies.length;
            }
            saveAppData();
            updateCategoriesCounts();
            renderCategories();
            document.getElementById('subcategories-export-status').textContent = `تم حذف ${removedCount} فيلم من الأقسام الفرعية.`;
        });
    }

    // --- قسم إدارة الأقسام الفرعية ---
    // تفعيل التبويب عند الضغط
    const subcategoriesTabBtn = document.querySelector('.tab-btn[data-tab="manage-subcategories"]');
    const subcategoriesTab = document.getElementById('manage-subcategories-tab');
    if (subcategoriesTabBtn && subcategoriesTab) {
        subcategoriesTabBtn.addEventListener('click', function() {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            subcategoriesTabBtn.classList.add('active');
            subcategoriesTab.classList.add('active');
        });
    }

    // زر تصدير الأقسام الفرعية
    const exportSubBtn = document.getElementById('export-subcategories-btn');
    if (exportSubBtn) {
        exportSubBtn.addEventListener('click', function() {
            const type = document.getElementById('subcategory-type-select').value;
            let categories = [];
            let categoryKey = '';
            if (type === 'sub') {
                categories = appState.categories.sub || [];
                categoryKey = 'sub';
            } else if (type === 'specialSub') {
                categories = appState.categories.specialSub || [];
                categoryKey = 'specialSub';
            }
            if (!categories.length) {
                document.getElementById('subcategories-export-status').textContent = 'لا توجد بيانات للتصدير.';
                return;
            }
            // جمع الأفلام الموجودة في هذه الأقسام الفرعية
            let movies = [];
            if (categoryKey) {
                movies = (appState.movies || []).filter(movie => {
                    if (categoryKey === 'sub') {
                        return movie.subCategories && movie.subCategories.some(subId => categories.some(cat => cat.id === subId));
                    } else if (categoryKey === 'specialSub') {
                        return movie.specialSubCategories && movie.specialSubCategories.some(subId => categories.some(cat => cat.id === subId));
                    }
                    return false;
                });
            }
            const exportObj = {
                categories: categories,
                movies: movies
            };
            const dataStr = JSON.stringify(exportObj, null, 2);
            const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
            // إضافة التاريخ إلى اسم الملف
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const fileName = type === 'sub'
                ? `public_subcategories_${dateStr}.json`
                : `private_subcategories_${dateStr}.json`;
            const link = document.createElement('a');
            link.setAttribute('href', dataUri);
            link.setAttribute('download', fileName);
            link.click();
            document.getElementById('subcategories-export-status').textContent = 'تم تصدير البيانات بنجاح.';
        });
    }

    // زر استيراد الأقسام الفرعية
    const importSubBtn = document.getElementById('import-subcategories-btn');
    const importSubFile = document.getElementById('import-subcategories-file');
    if (importSubBtn && importSubFile) {
        importSubBtn.addEventListener('click', function() {
            importSubFile.value = '';
            importSubFile.click();
        });
        importSubFile.addEventListener('change', function(e) {
            const type = document.getElementById('subcategory-type-select').value;
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = function(evt) {
                try {
                    const imported = JSON.parse(evt.target.result);
                    // دعم الصيغتين: القديمة (مصفوفة فقط) أو الجديدة (كائن فيه categories و movies)
                    let importedCategories = [];
                    let importedMovies = [];
                    if (Array.isArray(imported)) {
                        importedCategories = imported;
                    } else if (imported && Array.isArray(imported.categories)) {
                        importedCategories = imported.categories;
                        if (Array.isArray(imported.movies)) {
                            importedMovies = imported.movies;
                        }
                    } else {
                        throw new Error('الملف غير صحيح');
                    }

                    // تحديث أو إضافة الأقسام الفرعية
                    let targetCategories = type === 'sub' ? appState.categories.sub : appState.categories.specialSub;
                    importedCategories.forEach(importedCat => {
                        const idx = targetCategories.findIndex(cat => cat.id === importedCat.id);
                        if (idx !== -1) {
                            // تحديث القسم الموجود
                            targetCategories[idx] = { ...targetCategories[idx], ...importedCat };
                        } else {
                            // إضافة قسم جديد
                            targetCategories.push(importedCat);
                        }
                    });
                    if (type === 'sub') {
                        appState.categories.sub = targetCategories;
                    } else {
                        appState.categories.specialSub = targetCategories;
                    }

                    // تحديث أو إضافة الأفلام
                    if (importedMovies.length) {
                        importedMovies.forEach(movie => {
                            let foundIdx = appState.movies.findIndex(m => m.id === movie.id || m.name === movie.name);
                            if (foundIdx !== -1) {
                                // تحديث الفيلم الموجود
                                // تحديث الأقسام الفرعية فقط إذا كانت موجودة في القسم المستورد
                                if (type === 'sub' && Array.isArray(movie.subCategories)) {
                                    movie.subCategories = movie.subCategories.filter(subId => importedCategories.some(cat => cat.id === subId));
                                }
                                if (type === 'specialSub' && Array.isArray(movie.specialSubCategories)) {
                                    movie.specialSubCategories = movie.specialSubCategories.filter(subId => importedCategories.some(cat => cat.id === subId));
                                }
                                appState.movies[foundIdx] = { ...appState.movies[foundIdx], ...movie };
                            } else {
                                // إضافة فيلم جديد
                                if (type === 'sub' && Array.isArray(movie.subCategories)) {
                                    movie.subCategories = movie.subCategories.filter(subId => importedCategories.some(cat => cat.id === subId));
                                }
                                if (type === 'specialSub' && Array.isArray(movie.specialSubCategories)) {
                                    movie.specialSubCategories = movie.specialSubCategories.filter(subId => importedCategories.some(cat => cat.id === subId));
                                }
                                appState.movies.push(movie);
                            }
                        });
                    }

                    saveAppData();
                    updateCategoriesCounts();
                    renderCategories();
                    document.getElementById('subcategories-export-status').textContent = 'تم الاستيراد بنجاح.';
                } catch (err) {
                    document.getElementById('subcategories-export-status').textContent = 'فشل الاستيراد: ملف غير صالح.';
                }
            };
            reader.readAsText(file);
        });
    }
    // تغيير طريقة العرض (شبكي/قائمة)
    const viewModeSelect = document.getElementById('view-mode');
    viewModeSelect.value = appState.viewMode;
    viewModeSelect.addEventListener('change', () => {
        appState.viewMode = viewModeSelect.value;

        // حفظ موضع التمرير الحالي
        const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

        // استعادة موضع التمرير بعد تحديث الواجهة
        setTimeout(() => {
            window.scrollTo(0, currentScrollPosition);
        }, 50);

        saveAppData();
    });

    // تغيير طريقة الترتيب
    const sortOptionsSelect = document.getElementById('sort-options');
    sortOptionsSelect.value = appState.sortBy;

    // تحديث حقل الإدخال الرقمي للترتيب ليتطابق مع القيمة الحالية
    const sortInput = document.getElementById('sort-options-input');
    if (sortInput) {
        const sortMappings = { 'name': '1', 'site': '2', 'date': '3', 'date-asc': '4', 'star': '5' };
        if (sortMappings[appState.sortBy]) {
            sortInput.value = sortMappings[appState.sortBy];
        }
    }

    // إظهار/إخفاء خيار الترتيب حسب النجم عندما يكون القسم الحالي هو أفلام النجوم
    const starSortOption = document.querySelector('.star-sort-option');
    if (starSortOption) {
        if (appState.currentCategory === 'stars') {
            starSortOption.classList.remove('hidden');
        } else {
            starSortOption.classList.add('hidden');
        }
    }

    sortOptionsSelect.addEventListener('change', () => {
        const oldSortBy = appState.sortBy;
        appState.sortBy = sortOptionsSelect.value;

        // إعادة تعيين الفلاتر عند تغيير نوع الترتيب
        if (oldSortBy !== appState.sortBy) {
            appState.selectedSite = '';
            appState.selectedStar = '';
        }

        // إظهار/إخفاء خانات الفلترة حسب نوع الترتيب
        updateFilterVisibility();

        // حفظ موضع التمرير الحالي
        const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

        // استعادة موضع التمرير بعد تحديث الواجهة
        setTimeout(() => {
            window.scrollTo(0, currentScrollPosition);
        }, 50);

        saveAppData();
    });

    // إضافة مستمعي الأحداث لخانات الفلترة
    const siteFilter = document.getElementById('site-filter');
    const starFilter = document.getElementById('star-filter');

    if (siteFilter) {
        siteFilter.addEventListener('change', () => {
            appState.selectedSite = siteFilter.value;
            appState.currentPage = 1; // إعادة تعيين الصفحة إلى الأولى عند الفلترة
            displayMovies(appState.currentCategory, appState.currentPage); // هنا نريد الانتقال للأعلى عند الفلترة
            saveAppData();
        });
    }

    if (starFilter) {
        starFilter.addEventListener('change', () => {
            appState.selectedStar = starFilter.value;
            appState.currentPage = 1; // إعادة تعيين الصفحة إلى الأولى عند الفلترة
            displayMovies(appState.currentCategory, appState.currentPage); // هنا نريد الانتقال للأعلى عند الفلترة
            saveAppData();
        });
    }

    // زر الإعدادات
    document.getElementById('settings-btn').addEventListener('click', openSettingsModal);

    // البحث في التطبيق
    document.getElementById('search-app-btn').addEventListener('click', () => {
        const searchQuery = document.getElementById('search-input').value.trim();
        if (searchQuery.length > 0) {
            searchInApp(searchQuery);
        }
    });

    // البحث عند الضغط على Enter
    document.getElementById('search-input').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const searchQuery = e.target.value.trim();
            if (searchQuery.length > 0) {
                searchInApp(searchQuery);
            }
        }
    });

    // البحث في جوجل
    document.getElementById('search-google-btn').addEventListener('click', () => {
        const searchQuery = document.getElementById('search-input').value.trim();
        if (searchQuery.length > 0) {
            const fullSearchQuery = `مشاهدة فيلم ${searchQuery}`;
            window.open(`https://www.google.com/search?q=${encodeURIComponent(fullSearchQuery)}`, '_blank');
        }
    });

    // البحث في ياندكس
    document.getElementById('search-yandex-btn').addEventListener('click', () => {
        const searchQuery = document.getElementById('search-input').value.trim();
        if (searchQuery.length > 0) {
            const fullSearchQuery = `مشاهدة فيلم ${searchQuery}`;
            window.open(`https://yandex.com/search/?text=${encodeURIComponent(fullSearchQuery)}`, '_blank');
        }
    });

    // إغلاق نتائج البحث
    document.querySelector('.close-search').addEventListener('click', () => {
        document.getElementById('search-results').classList.add('hidden');
        // استعادة عنوان الصفحة المحفوظ
        document.title = appState.currentPageTitle || 'New Koktil-aflam v25';
    });

    // تعليق مستمعي إعدادات السكرول
    setupScrollHandlers();

    // تعليق مستمعي الأحداث للتكبير/التصغير
    setupZoomControls();

    // تعليق مستمعي إعدادات السحب والإفلات
    setupDropZones();

    // تهيئة أيقونات التنقل في الرأس
    setupHeaderNavigation();

    // تهيئة رؤية خانات الفلترة
    setTimeout(() => {
        updateFilterVisibility();

        // تطبيق القيم المحفوظة للفلاتر
        const siteFilter = document.getElementById('site-filter');
        const starFilter = document.getElementById('star-filter');
        if (siteFilter && appState.selectedSite) siteFilter.value = appState.selectedSite;
        if (starFilter && appState.selectedStar) starFilter.value = appState.selectedStar;

        // تحديث حقول الإدخال الرقمية
        if (typeof updateNumberInputsFromSelects === 'function') {
            updateNumberInputsFromSelects();
        }
    }, 100);
}

// تهيئة أيقونات التنقل
function setupHeaderNavigation() {
    const headerNavigation = document.querySelector('.header-navigation');

    // إخفاء أيقونات التنقل في البداية حتى يتم تحميل البيانات
    if (headerNavigation) {
        headerNavigation.style.display = 'none';
    }

    // إضافة دعم لوحة المفاتيح للتنقل
    document.addEventListener('keydown', (event) => {
        // التأكد من أن المستخدم لا يكتب في حقل إدخال
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }

        const totalPages = Math.ceil(getTotalItemsCount() / appState.itemsPerPage);

        // السهم الأيسر أو مفتاح A للصفحة السابقة
        if ((event.key === 'ArrowLeft' || event.key === 'a' || event.key === 'A') && appState.currentPage > 1) {
            event.preventDefault();
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage - 1);
        }

        // السهم الأيمن أو مفتاح D للصفحة التالية
        if ((event.key === 'ArrowRight' || event.key === 'd' || event.key === 'D') && appState.currentPage < totalPages) {
            event.preventDefault();
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, appState.currentPage + 1);
        }

        // مفتاح Home للصفحة الأولى
        if (event.key === 'Home' && appState.currentPage > 1) {
            event.preventDefault();
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, 1);
        }

        // مفتاح End للصفحة الأخيرة
        if (event.key === 'End' && appState.currentPage < totalPages) {
            event.preventDefault();
            scrollToTopImmediate();
            displayMovies(appState.currentCategory, totalPages);
        }
    });
}

// دالة مساعدة للحصول على العدد الإجمالي للعناصر
function getTotalItemsCount() {
    let items = [];

    if (appState.currentCategory === 'all') {
        items = [...appState.movies, ...appState.series];
    } else {
        const categoryObj = [...appState.categories.main, ...appState.categories.sub,
                             ...appState.categories.special, ...appState.categories.specialSub]
                            .find(cat => cat.id === appState.currentCategory);

        if (categoryObj) {
            items = categoryObj.items || [];
        }
    }

    // تطبيق الفلاتر
    items = applyFilters(items);

    return items.length;
}

// إعداد مستمعي الأحداث للتكبير/التصغير
function setupZoomControls() {
    const zoomInBtn = document.getElementById('zoom-in-btn');
    const zoomOutBtn = document.getElementById('zoom-out-btn');
    const zoomResetBtn = document.getElementById('zoom-reset-btn');

    if (zoomInBtn && zoomOutBtn && zoomResetBtn) {
        zoomInBtn.addEventListener('click', () => {
            // حد أقصى تكبير بمقدار الضعف (2.0) بدرجات 5%
            if (appState.zoomLevel < 2.0) {
                appState.zoomLevel += 0.05; // زيادة بمقدار 5%
                // التأكد من عدم تجاوز الحد الأقصى
                if (appState.zoomLevel > 2.0) {
                    appState.zoomLevel = 2.0;
                }
                applyZoom();
                saveAppData(); // حفظ مستوى التكبير
            }
        });

        zoomOutBtn.addEventListener('click', () => {
            // حد أدنى تصغير بمقدار النصف (0.5) بدرجات 5%
            if (appState.zoomLevel > 0.5) {
                appState.zoomLevel -= 0.05; // تقليل بمقدار 5%
                // التأكد من عدم النزول تحت 0.5
                if (appState.zoomLevel < 0.5) {
                    appState.zoomLevel = 0.5;
                }
                applyZoom();
                saveAppData(); // حفظ مستوى التكبير
            }
        });

        zoomResetBtn.addEventListener('click', () => {
            appState.zoomLevel = 1;
            applyZoom();
            saveAppData(); // حفظ مستوى التكبير
        });
    }
}

// تطبيق مستوى التكبير
function applyZoom() {
    // تطبيق التكبير على حجم الخط الأساسي
    document.documentElement.style.fontSize = `${16 * appState.zoomLevel}px`;

    // تطبيق التكبير على الصفحة بأكملها مع توزيع متناسق
    const appContainer = document.querySelector('.app-container');
    const body = document.body;
    const html = document.documentElement;

    if (appState.zoomLevel !== 1) {
        // تطبيق التكبير على الحاوية الرئيسية
        if (appContainer) {
            appContainer.style.transform = `scale(${appState.zoomLevel})`;
            appContainer.style.transformOrigin = 'top center';
            appContainer.style.width = `${100 / appState.zoomLevel}%`;
            appContainer.style.margin = '0 auto';
        }

        // تعديل إعدادات الجسم والـ HTML
        body.style.overflowX = 'auto';
        html.style.overflowX = 'auto';

        // ضمان أن الخلفية تغطي كامل الشاشة
        body.style.minWidth = '100%';
        body.style.backgroundColor = 'var(--main-bg-color)';

        // تعديل موضع أزرار التكبير/التصغير لتبقى في مكانها
        const zoomControls = document.querySelector('.zoom-controls');
        if (zoomControls) {
            zoomControls.style.transform = `scale(${1 / appState.zoomLevel})`;
            zoomControls.style.left = `${20 / appState.zoomLevel}px`;
            zoomControls.style.bottom = `${20 / appState.zoomLevel}px`;
        }
    } else {
        // إعادة تعيين القيم الافتراضية
        if (appContainer) {
            appContainer.style.transform = '';
            appContainer.style.transformOrigin = '';
            appContainer.style.width = '';
            appContainer.style.margin = '';
        }

        body.style.overflowX = '';
        html.style.overflowX = '';
        body.style.minWidth = '';
        body.style.backgroundColor = '';

        // إعادة تعيين أزرار التكبير/التصغير
        const zoomControls = document.querySelector('.zoom-controls');
        if (zoomControls) {
            zoomControls.style.transform = '';
            zoomControls.style.left = '';
            zoomControls.style.bottom = '';
        }
    }

    // تحديث أزرار التكبير/التصغير لإظهار الحالة الحالية
    updateZoomButtonsState();
}

// تحديث حالة أزرار التكبير/التصغير
function updateZoomButtonsState() {
    const zoomInBtn = document.getElementById('zoom-in-btn');
    const zoomOutBtn = document.getElementById('zoom-out-btn');

    if (zoomInBtn) {
        zoomInBtn.disabled = appState.zoomLevel >= 2.0;
        zoomInBtn.style.opacity = appState.zoomLevel >= 2.0 ? '0.5' : '1';
    }

    if (zoomOutBtn) {
        zoomOutBtn.disabled = appState.zoomLevel <= 0.5;
        zoomOutBtn.style.opacity = appState.zoomLevel <= 0.5 ? '0.5' : '1';
    }
}

// إعداد مستمعي إعدادات السكرول
function setupScrollHandlers() {
    let lastScrollPosition = 0;
    const header = document.getElementById('app-header');

    window.addEventListener('scroll', () => {
        const currentScrollPosition = window.pageYOffset;

        // إخفاء/إظهار الرأس عند التمرير
        if (currentScrollPosition > lastScrollPosition && currentScrollPosition > 50) {
            // تمرير لأسفل
            header.classList.add('hidden');
        } else if (currentScrollPosition < lastScrollPosition || currentScrollPosition < 50) {
            // تمرير لأعلى
            header.classList.remove('hidden');
        }

        lastScrollPosition = currentScrollPosition;
    });
}

// إعداد مستمعي إعدادات السحب والإفلات
function setupDropZones() {
    console.log('إعداد مناطق السحب والإفلات...');

    const dropZones = [
        document.getElementById('s3-dropzone'),
        document.getElementById('s-sites-dropzone'),
        document.getElementById('import-dropzone'),
        document.getElementById('movies-import-dropzone')
    ];

    // إضافة مستمعي الأحداث لجميع مناطق السحب والإفلات
    dropZones.forEach(zone => {
        if (zone) {
            console.log(`إعداد منطقة السحب والإفلات: ${zone.id}`);

            // منع السلوك الافتراضي للسحب والإفلات
            zone.addEventListener('dragenter', (e) => {
                e.preventDefault();
                e.stopPropagation();
                zone.classList.add('dragover');
                console.log(`دخول السحب في: ${zone.id}`);
            });

            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.stopPropagation();
                zone.classList.add('dragover');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                e.stopPropagation();
                // التحقق من أن المؤشر خرج من المنطقة فعلاً
                if (!zone.contains(e.relatedTarget)) {
                    zone.classList.remove('dragover');
                    console.log(`خروج السحب من: ${zone.id}`);
                }
            });

            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                zone.classList.remove('dragover');
                console.log(`إفلات في: ${zone.id}`);

                // إظهار رسالة فورية للسحب والإفلات
                if (zone.id === 's3-dropzone') {
                    console.log('معالجة الإفلات في قسم S3');
                    showToast('📥 تم استلام البيانات في قسم الاكس S3', 'info');
                    setTimeout(() => {
                        handleDropToS3Zone(e.dataTransfer);
                    }, 100);
                } else if (zone.id === 's-sites-dropzone') {
                    console.log('معالجة الإفلات في قسم S SITES');
                    showToast('📥 تم استلام البيانات في قسم S SITES', 'info');
                    setTimeout(() => {
                        handleDropToSitesZone(e.dataTransfer);
                    }, 100);
                } else if (zone.id === 'import-dropzone') {
                    handleImportDrop(e.dataTransfer);
                } else if (zone.id === 'movies-import-dropzone') {
                    handleMoviesImportDrop(e.dataTransfer);
                }
            });
        } else {
            console.warn(`منطقة السحب والإفلات غير موجودة`);
        }
    });

    // إعداد أزرار الإضافة
    const s3AddBtn = document.getElementById('s3-add-btn');
    const sSitesAddBtn = document.getElementById('s-sites-add-btn');

    if (s3AddBtn) {
        console.log('إعداد زر إضافة S3');
        s3AddBtn.addEventListener('click', () => {
            const linksInput = document.getElementById('s3-links-input');
            if (linksInput && linksInput.value.trim()) {
                console.log('إضافة روابط S3:', linksInput.value.trim());
                handleS3Links(linksInput.value.trim());
                linksInput.value = '';
            } else {
                showToast('يرجى إدخال روابط صحيحة', 'warning');
            }
        });
    } else {
        console.warn('زر إضافة S3 غير موجود');
    }

    if (sSitesAddBtn) {
        console.log('إعداد زر إضافة S SITES');
        sSitesAddBtn.addEventListener('click', () => {
            const linksInput = document.getElementById('s-sites-links-input');
            if (linksInput && linksInput.value.trim()) {
                console.log('إضافة روابط S SITES:', linksInput.value.trim());
                handleSitesLinks(linksInput.value.trim());
                linksInput.value = '';
            } else {
                showToast('يرجى إدخال روابط صحيحة', 'warning');
            }
        });
    } else {
        console.warn('زر إضافة S SITES غير موجود');
    }

    // إضافة دعم للصق الروابط مباشرة في حقول النص
    const s3Input = document.getElementById('s3-links-input');
    const sSitesInput = document.getElementById('s-sites-links-input');

    if (s3Input) {
        s3Input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (s3Input.value.trim()) {
                    handleS3Links(s3Input.value.trim());
                    s3Input.value = '';
                }
            }
        });
    }

    if (sSitesInput) {
        sSitesInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (sSitesInput.value.trim()) {
                    handleSitesLinks(sSitesInput.value.trim());
                    sSitesInput.value = '';
                }
            }
        });
    }
}

// معالجة السحب والإفلات لاستيراد الأفلام
function handleMoviesImportDrop(dataTransfer) {
    const files = dataTransfer.files;
    if (files && files.length > 0) {
        handleMoviesImportFiles(files);
    }
}

// معالجة ملفات استيراد الأفلام
function handleMoviesImportFiles(files) {
    // منع الاستدعاءات المتكررة
    if (handleMoviesImportFiles._isRunning) {
        showToast('جاري استيراد ملفات أخرى، يرجى الانتظار', 'warning');
        return;
    }
    handleMoviesImportFiles._isRunning = true;

    const importCategory = document.getElementById('import-category').value;
    if (!importCategory) {
        showToast('يرجى اختيار قسم للاستيراد إليه', 'warning');
        handleMoviesImportFiles._isRunning = false;
        return;
    }

    // الحصول على اسم النجم المدخل إذا كان القسم هو أفلام النجوم
    const defaultStarName = importCategory === 'stars' ?
        document.getElementById('import-star-name').value.trim() : '';

    // قراءة كل ملف كـ JSON
    let filesProcessed = 0;
    let totalImportedItems = 0;
    let totalItemsCount = 0;
    let duplicatesCount = 0;

    // إنشاء وإظهار مؤشر التقدم
    createProgressIndicator('movies-import-progress');

    // استخدام setTimeout لتجنب تهنيج الصفحة
    const processFile = (index) => {
        if (index >= files.length) {
            // انتهت معالجة جميع الملفات
            saveAppData();
            updateCategoriesCounts();
            renderCategories();

            if (appState.currentCategory === importCategory) {
                displayMovies(importCategory);
            }

            // إزالة مؤشر التقدم بعد انتهاء الاستيراد
            setTimeout(() => {
                // تأكد من أن مؤشر التقدم لا يزال موجودًا قبل إزالته
                if (document.getElementById('movies-import-progress')) {
                    removeProgressIndicator('movies-import-progress');
                    let message = `تم استيراد ${totalImportedItems} فيلم من ${filesProcessed} ملف بنجاح`;
                    if (duplicatesCount > 0) {
                        message += ` (تم تجاهل ${duplicatesCount} أفلام مكررة)`;
                    }
                    showToast(message, 'success');

                    // تحديث فلاتر النجوم إذا تم الاستيراد لقسم أفلام النجوم
                    if (importCategory === 'stars' && appState.currentCategory === 'stars' && appState.sortBy === 'star') {
                        updateFilterVisibility();
                    }
                }
                // إعادة تعيين الحماية
                handleMoviesImportFiles._isRunning = false;
            }, 500);
            return;
        }

        // تحديث مؤشر التقدم قبل بدء معالجة الملف
        updateProgressIndicator('movies-import-progress', totalImportedItems, Math.max(totalItemsCount, 1),
            `استيراد الأفلام (${totalImportedItems}/${Math.max(totalItemsCount, 1)})`);

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                // حساب إجمالي عدد العناصر
                const itemsCount = countItemsInData(data);
                totalItemsCount += itemsCount;

                // تحديث مؤشر التقدم
                updateProgressIndicator('movies-import-progress', totalImportedItems, totalItemsCount,
                    `استيراد الأفلام (${totalImportedItems}/${totalItemsCount})`);

                // استخدام setTimeout لتجنب تهنيج الصفحة أثناء معالجة البيانات
                setTimeout(async () => {
                    try {
                        const result = await importMoviesData(data, importCategory, defaultStarName);
                        totalImportedItems += result.importedCount;
                        duplicatesCount += result.duplicatesCount;
                        filesProcessed++;

                        // تحديث مؤشر التقدم
                        updateProgressIndicator('movies-import-progress', totalImportedItems, totalItemsCount,
                            `استيراد الأفلام (${totalImportedItems}/${totalItemsCount})`);

                        // معالجة الملف التالي
                        setTimeout(() => processFile(index + 1), 10);
                    } catch (error) {
                        console.error('خطأ في استيراد البيانات:', error);
                        showToast(`خطأ في استيراد البيانات من الملف: ${files[index].name}`, 'error');
                        filesProcessed++;
                        setTimeout(() => processFile(index + 1), 10);
                    }
                }, 10);
            } catch (error) {
                console.error('خطأ في تحليل ملف JSON:', error);
                showToast(`خطأ في تحليل ملف JSON: ${files[index].name}`, 'error');
                filesProcessed++;

                // تحديث مؤشر التقدم حتى في حالة الخطأ
                updateProgressIndicator('movies-import-progress', totalImportedItems, Math.max(totalItemsCount, 1),
                    `استيراد الأفلام (${totalImportedItems}/${Math.max(totalItemsCount, 1)}) - خطأ في الملف الحالي`);

                // معالجة الملف التالي
                setTimeout(() => processFile(index + 1), 10);
            } finally {
                // إعادة تعيين الحماية في حالة انتهاء جميع الملفات أو حدوث خطأ
                if (index >= files.length - 1) {
                    setTimeout(() => {
                        handleMoviesImportFiles._isRunning = false;
                    }, 1000);
                }
            }
        };

        reader.onerror = (e) => {
            console.error('خطأ في قراءة الملف:', e);
            showToast(`خطأ في قراءة الملف: ${files[index].name}`, 'error');
            filesProcessed++;

            // تحديث مؤشر التقدم حتى في حالة الخطأ
            updateProgressIndicator('movies-import-progress', totalImportedItems, Math.max(totalItemsCount, 1),
                `استيراد الأفلام (${totalImportedItems}/${Math.max(totalItemsCount, 1)}) - خطأ في قراءة الملف`);

            // معالجة الملف التالي
            setTimeout(() => processFile(index + 1), 10);

            // إعادة تعيين الحماية في حالة انتهاء جميع الملفات
            if (index >= files.length - 1) {
                setTimeout(() => {
                    handleMoviesImportFiles._isRunning = false;
                }, 1000);
            }
        };

        try {
            reader.readAsText(files[index]);
        } catch (error) {
            console.error('خطأ في بدء قراءة الملف:', error);
            showToast(`خطأ في قراءة الملف: ${files[index].name}`, 'error');
            filesProcessed++;

            // تحديث مؤشر التقدم حتى في حالة الخطأ
            updateProgressIndicator('movies-import-progress', totalImportedItems, Math.max(totalItemsCount, 1),
                `استيراد الأفلام (${totalImportedItems}/${Math.max(totalItemsCount, 1)}) - خطأ في قراءة الملف`);

            // معالجة الملف التالي
            setTimeout(() => processFile(index + 1), 10);

            // إعادة تعيين الحماية في حالة انتهاء جميع الملفات
            if (index >= files.length - 1) {
                setTimeout(() => {
                    handleMoviesImportFiles._isRunning = false;
                }, 1000);
            }
        }
    };

    // بدء معالجة الملفات
    processFile(0);
}

// استيراد بيانات الأفلام إلى قسم محدد
async function importMoviesData(data, categoryId, defaultStarName = '') {
    let importedCount = 0;
    let duplicatesCount = 0;

    // البحث عن البيانات الصحيحة
    if (data.series_info) {
        // استيراد المسلسلات
        for (const series of data.series_info) {
            const newSeries = {
                id: generateUniqueId(),
                name: series.series_name || series.movies_name || series.title || '',
                img: series.series_img || series.movies_img || series.imageUrl || '',
                href: series.series_href || series.movies_href || series.link || '',
                category: 'series',
                addedDate: new Date().toISOString(),
                hidden: false
            };

            // إضافة الأقسام الفرعية إذا كانت موجودة في البيانات المستوردة
            if (series.subCategories && Array.isArray(series.subCategories)) {
                newSeries.subCategories = [...series.subCategories];
            }

            if (!isDuplicateMovieInArray(newSeries, appState.series)) {
                appState.series.push(newSeries);
                importedCount++;
            } else {
                duplicatesCount++;
            }
        }
    } else if (data.movies_info) {
        // استيراد الأفلام مع معالجة على دفعات للملفات الكبيرة
        const batchSize = 50; // حجم الدفعة
        for (let i = 0; i < data.movies_info.length; i += batchSize) {
            const batch = data.movies_info.slice(i, i + batchSize);

            for (const movie of batch) {
                const newMovie = {
                    id: generateUniqueId(),
                    name: movie.movies_name || movie.title || '',
                    img: movie.movies_img || movie.imageUrl || '',
                    href: movie.movies_href || movie.link || '',
                    category: categoryId,
                    addedDate: new Date().toISOString(),
                    hidden: false
                };

                // إضافة اسم النجم إذا كان القسم هو "أفلام النجوم"
                if (categoryId === 'stars') {
                    newMovie.starName = movie.starName || movie.star_name || defaultStarName || '';
                }

                // إضافة الأقسام الفرعية إذا كانت موجودة في البيانات المستوردة
                if (movie.subCategories && Array.isArray(movie.subCategories)) {
                    newMovie.subCategories = [...movie.subCategories];
                } else if (categoryId.startsWith('selected') || categoryId.startsWith('favorite') || categoryId.startsWith('selected-rs')) {
                    // إذا كان القسم المستهدف هو قسم فرعي، أضف الفيلم إلى هذا القسم الفرعي
                    newMovie.subCategories = [categoryId];
                }

                if (!isDuplicateMovieInArray(newMovie, appState.movies)) {
                    appState.movies.push(newMovie);
                    importedCount++;
                } else {
                    duplicatesCount++;
                }
            }

            // إعطاء فرصة للمتصفح للتنفس بين الدفعات
            if (i + batchSize < data.movies_info.length) {
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        }
    } else if (Array.isArray(data)) {
        // أنماط بديلة من الأرايات
        for (const item of data) {
            const movieData = {
                id: generateUniqueId(),
                addedDate: new Date().toISOString(),
                hidden: false,
                category: categoryId
            };

            // محاولة استخراج البيانات من الأنماط المختلفة
            if (item.movies_name || item.series_name || item.title || item.name) {
                movieData.name = item.movies_name || item.series_name || item.title || item.name || '';
            }

            if (item.movies_img || item.series_img || item.imageUrl || item.img) {
                movieData.img = item.movies_img || item.series_img || item.imageUrl || item.img || '';
            }

            if (item.movies_href || item.series_href || item.link || item.href) {
                movieData.href = item.movies_href || item.series_href || item.link || item.href || '';
            }

            // إضافة اسم النجم إذا كان القسم هو "أفلام النجوم"
            if (categoryId === 'stars') {
                movieData.starName = item.starName || item.star_name || defaultStarName || '';
            }

            // إضافة الأقسام الفرعية إذا كانت موجودة في البيانات المستوردة
            if (item.subCategories && Array.isArray(item.subCategories)) {
                movieData.subCategories = [...item.subCategories];
            } else if (categoryId.startsWith('selected') || categoryId.startsWith('favorite') || categoryId.startsWith('selected-rs')) {
                // إذا كان القسم المستهدف هو قسم فرعي، أضف الفيلم إلى هذا القسم الفرعي
                movieData.subCategories = [categoryId];
            }

            if (!isDuplicateMovieInArray(movieData, appState.movies)) {
                appState.movies.push(movieData);
                importedCount++;
            } else {
                duplicatesCount++;
            }
        }
    }

    return { importedCount, duplicatesCount };
}

// التحقق من وجود فيلم مكرر في مصفوفة
function isDuplicateMovieInArray(movie, array) {
    // استثناء للأفلام في قسم أفلام النجوم - السماح بإضافة الأفلام المكررة
    if (movie.category === 'stars') {
        return false;
    }

    return array.some(item =>
        (item.name === movie.name && item.name !== '') ||
        (item.href === movie.href && item.href !== '')
    );
}

// إضافة فيلم جديد
function addNewMovie() {
    const nameInput = document.getElementById('movie-name');
    const imgInput = document.getElementById('movie-img');
    const hrefInput = document.getElementById('movie-href');
    const categorySelect = document.getElementById('movie-category');
    const starNameInput = document.getElementById('star-name');

    const name = nameInput.value.trim();
    const img = imgInput.value.trim();
    const href = hrefInput.value.trim();
    const category = categorySelect.value;

    if (!name) {
        showToast('يرجى إدخال اسم الفيلم/المسلسل', 'warning');
        return;
    }

    if (!category) {
        showToast('يرجى اختيار قسم', 'warning');
        return;
    }

    // التحقق من وجود نسخة مكررة من الفيلم
    if (isDuplicateMovie(name, href, category)) {
        showToast('هذا الفيلم موجود بالفعل في هذا القسم', 'warning');
        return;
    }

    // إنشاء بيانات الفيلم/المسلسل
    const movieData = {
        id: generateUniqueId(),
        name: name,
        img: img,
        href: href,
        category: category,
        addedDate: new Date().toISOString(),
        hidden: false
    };

    // إضافة اسم النجم إذا كان القسم هو "أفلام النجوم"
    if (category === 'stars') {
        movieData.starName = starNameInput.value.trim();
    }

    // إضافة الفيلم/المسلسل إلى المصفوفة المناسبة
    if (category === 'series') {
        appState.series.push(movieData);
    } else {
        appState.movies.push(movieData);
    }

    // حفظ البيانات وتحديث الواجهة
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // إعادة تعيين النموذج
    nameInput.value = '';
    imgInput.value = '';
    hrefInput.value = '';
    starNameInput.value = '';

    showToast(`تمت إضافة "${name}" بنجاح`, 'success');

    // تحديث العرض إذا كان القسم الحالي هو نفس القسم المضاف إليه
    if (appState.currentCategory === category || appState.currentCategory === 'all') {
        displayMovies(appState.currentCategory, appState.currentPage);
    }

    // تحديث فلاتر النجوم إذا تم إضافة فيلم لقسم أفلام النجوم
    if (category === 'stars' && appState.currentCategory === 'stars' && appState.sortBy === 'star') {
        updateFilterVisibility();
    }
}

// التحقق من وجود فيلم مكرر
function isDuplicateMovie(name, href, category) {
    // التحقق من وجود الاسم والرابط معاً في نفس القسم
    if (category === 'series') {
        return appState.series.some(item =>
            (item.name === name || item.href === href) &&
            item.category === category &&
            !item.hidden
        );
    } else {
        return appState.movies.some(item =>
            (item.name === name || item.href === href) &&
            item.category === category &&
            !item.hidden
        );
    }
}

// معالجة السحب والإفلات لقسم S3
function handleDropToS3Zone(dataTransfer) {
    console.log('معالجة البيانات المسحوبة لقسم S3');

    // إظهار رسالة بداية المعالجة
    showToast('🔍 جاري فحص البيانات المسحوبة لقسم الاكس S3...', 'info');

    // محاولة الحصول على النص بطرق مختلفة
    let text = dataTransfer.getData('text/plain') ||
               dataTransfer.getData('text/uri-list') ||
               dataTransfer.getData('text/html');

    console.log('النص المسحوب:', text);

    if (text) {
        // تنظيف النص من HTML tags إذا كان موجوداً
        if (text.includes('<')) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;
            text = tempDiv.textContent || tempDiv.innerText || '';
        }

        if (text.trim()) {
            console.log('إرسال النص لمعالجة S3:', text.trim());
            showToast('🔄 جاري معالجة الروابط وإضافتها لقسم الاكس S3...', 'info');

            // تأخير قصير لضمان ظهور الرسالة قبل المعالجة
            setTimeout(() => {
                handleS3Links(text.trim());
            }, 200);
        } else {
            showToast('⚠️ لم يتم العثور على نص صالح للإضافة لقسم الاكس S3', 'warning');
        }
    } else {
        console.warn('لم يتم العثور على بيانات نصية في الإفلات');
        showToast('❌ فشل في السحب والإفلات لقسم الاكس S3\n🔍 السبب: لم يتم العثور على نص أو روابط في العنصر المسحوب\n💡 الحل: تأكد من سحب نص يحتوي على روابط صحيحة', 'error');
    }
}

// معالجة السحب والإفلات لقسم S SITES
function handleDropToSitesZone(dataTransfer) {
    console.log('معالجة البيانات المسحوبة لقسم S SITES');

    // إظهار رسالة بداية المعالجة
    showToast('🔍 جاري فحص البيانات المسحوبة لقسم S SITES...', 'info');

    // محاولة الحصول على النص بطرق مختلفة
    let text = dataTransfer.getData('text/plain') ||
               dataTransfer.getData('text/uri-list') ||
               dataTransfer.getData('text/html');

    console.log('النص المسحوب:', text);

    if (text) {
        // تنظيف النص من HTML tags إذا كان موجوداً
        if (text.includes('<')) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;
            text = tempDiv.textContent || tempDiv.innerText || '';
        }

        if (text.trim()) {
            console.log('إرسال النص لمعالجة S SITES:', text.trim());
            showToast('🔄 جاري معالجة الروابط وإضافتها لقسم S SITES...', 'info');

            // تأخير قصير لضمان ظهور الرسالة قبل المعالجة
            setTimeout(() => {
                handleSitesLinks(text.trim());
            }, 200);
        } else {
            showToast('⚠️ لم يتم العثور على نص صالح للإضافة لقسم S SITES', 'warning');
        }
    } else {
        console.warn('لم يتم العثور على بيانات نصية في الإفلات');
        showToast('❌ فشل في السحب والإفلات لقسم S SITES\n🔍 السبب: لم يتم العثور على نص أو روابط في العنصر المسحوب\n💡 الحل: تأكد من سحب نص يحتوي على روابط صحيحة', 'error');
    }
}

// معالجة روابط قسم S3
function handleS3Links(linksText) {
    console.log('بدء معالجة روابط قسم S3:', linksText);

    if (!linksText || !linksText.trim()) {
        showToast('❌ فشل في إضافة الروابط لقسم الاكس S3\n🔍 السبب: لم يتم إدخال أي نص أو روابط\n💡 الحل: قم بلصق روابط الأفلام في الحقل المخصص', 'error');
        return;
    }

    // تقسيم النص إلى روابط (دعم أسطر متعددة ومسافات)
    const links = linksText.split(/[\n\r\s]+/).filter(link => {
        const trimmed = link.trim();
        // التحقق من أن النص يبدو كرابط
        return trimmed && (trimmed.startsWith('http') || trimmed.includes('.'));
    });

    console.log('الروابط المستخرجة:', links);

    if (links.length === 0) {
        showToast('❌ فشل في إضافة الروابط لقسم الاكس S3\n🔍 السبب: النص المدخل لا يحتوي على روابط صالحة\n💡 الحل: تأكد من إدخال روابط تبدأ بـ http أو تحتوي على نقاط (.)', 'error');
        return;
    }

    let addedCount = 0;
    let duplicatesCount = 0;
    let invalidCount = 0;

    links.forEach(link => {
        const trimmedLink = link.trim();
        if (trimmedLink) {
            try {
                // التحقق من صحة الرابط
                new URL(trimmedLink.startsWith('http') ? trimmedLink : 'http://' + trimmedLink);

                // استخراج اسم الفيلم من الرابط
                const movieName = extractMovieNameFromUrl(trimmedLink);

                // التحقق من عدم وجود الفيلم مسبقاً
                if (!isDuplicateMovie(movieName, trimmedLink, 's3')) {
                    const newMovie = {
                        id: generateUniqueId(),
                        name: movieName,
                        img: '', // يمكن إضافة صورة افتراضية لاحقاً
                        href: trimmedLink.startsWith('http') ? trimmedLink : 'http://' + trimmedLink,
                        category: 's3',
                        addedDate: new Date().toISOString(),
                        hidden: false
                    };

                    appState.movies.push(newMovie);
                    addedCount++;
                    console.log('تمت إضافة فيلم جديد:', movieName);
                } else {
                    duplicatesCount++;
                    console.log('فيلم مكرر:', movieName);
                }
            } catch (error) {
                invalidCount++;
                console.warn('رابط غير صحيح:', trimmedLink);
            }
        }
    });

    // حفظ البيانات وتحديث الواجهة
    if (addedCount > 0) {
        saveAppData();
        updateCategoriesCounts();
        renderCategories();

        // تحديث العرض إذا كان القسم الحالي هو S3
        if (appState.currentCategory === 's3') {
            displayMovies('s3', appState.currentPage);
        }
    }

    // إظهار رسائل تنبيه مفصلة للنجاح والفشل
    if (addedCount > 0) {
        // رسالة النجاح الأساسية
        let successMessage = `🎉 نجحت العملية! تم إضافة ${addedCount} فيلم جديد إلى قسم الاكس S3`;

        // إضافة تفاصيل إضافية إذا وجدت
        let detailsMessage = '';
        if (duplicatesCount > 0) {
            detailsMessage += `\n📋 تم تجاهل ${duplicatesCount} فيلم مكرر (موجود مسبقاً)`;
        }
        if (invalidCount > 0) {
            detailsMessage += `\n🚫 تم تجاهل ${invalidCount} رابط غير صالح`;
        }

        showToast(successMessage + detailsMessage, 'success');

        // رسالة تأكيد إضافية بعد 3 ثوان
        setTimeout(() => {
            showToast(`✨ تم! الأفلام الجديدة متاحة الآن في قسم الاكس S3 ويمكنك مشاهدتها`, 'success');
        }, 3000);

    } else {
        // رسائل الفشل مع أسباب مفصلة
        let failureMessage = '❌ فشلت عملية الإضافة لقسم الاكس S3';
        let reasonMessage = '';

        if (duplicatesCount > 0 && invalidCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط إما مكررة (${duplicatesCount}) أو غير صالحة (${invalidCount})`;
            failureMessage += '\n💡 الحل: تأكد من إدخال روابط جديدة وصحيحة';
        } else if (duplicatesCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط (${duplicatesCount}) موجودة مسبقاً في القسم`;
            failureMessage += '\n💡 الحل: جرب روابط أفلام جديدة لم تتم إضافتها من قبل';
        } else if (invalidCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط (${invalidCount}) غير صالحة أو تالفة`;
            failureMessage += '\n💡 الحل: تأكد من صحة الروابط وأنها تبدأ بـ http أو https';
        } else {
            reasonMessage = '\n🔍 السبب: لم يتم العثور على أي روابط صالحة في النص المدخل';
            failureMessage += '\n💡 الحل: تأكد من لصق روابط صحيحة أو سحب نص يحتوي على روابط';
        }

        showToast(failureMessage + reasonMessage, 'error');
    }
}

// معالجة روابط قسم S SITES
function handleSitesLinks(linksText) {
    console.log('بدء معالجة روابط قسم S SITES:', linksText);

    if (!linksText || !linksText.trim()) {
        showToast('❌ فشل في إضافة الروابط لقسم S SITES\n🔍 السبب: لم يتم إدخال أي نص أو روابط\n💡 الحل: قم بلصق روابط الأفلام في الحقل المخصص', 'error');
        return;
    }

    // تقسيم النص إلى روابط (دعم أسطر متعددة ومسافات)
    const links = linksText.split(/[\n\r\s]+/).filter(link => {
        const trimmed = link.trim();
        // التحقق من أن النص يبدو كرابط
        return trimmed && (trimmed.startsWith('http') || trimmed.includes('.'));
    });

    console.log('الروابط المستخرجة:', links);

    if (links.length === 0) {
        showToast('❌ فشل في إضافة الروابط لقسم S SITES\n🔍 السبب: النص المدخل لا يحتوي على روابط صالحة\n💡 الحل: تأكد من إدخال روابط تبدأ بـ http أو تحتوي على نقاط (.)', 'error');
        return;
    }

    let addedCount = 0;
    let duplicatesCount = 0;
    let invalidCount = 0;

    links.forEach(link => {
        const trimmedLink = link.trim();
        if (trimmedLink) {
            try {
                // التحقق من صحة الرابط
                new URL(trimmedLink.startsWith('http') ? trimmedLink : 'http://' + trimmedLink);

                // استخراج اسم الفيلم من الرابط
                const movieName = extractMovieNameFromUrl(trimmedLink);

                // التحقق من عدم وجود الفيلم مسبقاً
                if (!isDuplicateMovie(movieName, trimmedLink, 's-sites')) {
                    const newMovie = {
                        id: generateUniqueId(),
                        name: movieName,
                        img: '', // يمكن إضافة صورة افتراضية لاحقاً
                        href: trimmedLink.startsWith('http') ? trimmedLink : 'http://' + trimmedLink,
                        category: 's-sites',
                        addedDate: new Date().toISOString(),
                        hidden: false
                    };

                    appState.movies.push(newMovie);
                    addedCount++;
                    console.log('تمت إضافة فيلم جديد:', movieName);
                } else {
                    duplicatesCount++;
                    console.log('فيلم مكرر:', movieName);
                }
            } catch (error) {
                invalidCount++;
                console.warn('رابط غير صحيح:', trimmedLink);
            }
        }
    });

    // حفظ البيانات وتحديث الواجهة
    if (addedCount > 0) {
        saveAppData();
        updateCategoriesCounts();
        renderCategories();

        // تحديث العرض إذا كان القسم الحالي هو S SITES
        if (appState.currentCategory === 's-sites') {
            displayMovies('s-sites', appState.currentPage);
        }
    }

    // إظهار رسائل تنبيه مفصلة للنجاح والفشل
    if (addedCount > 0) {
        // رسالة النجاح الأساسية
        let successMessage = `🎉 نجحت العملية! تم إضافة ${addedCount} فيلم جديد إلى قسم S SITES`;

        // إضافة تفاصيل إضافية إذا وجدت
        let detailsMessage = '';
        if (duplicatesCount > 0) {
            detailsMessage += `\n📋 تم تجاهل ${duplicatesCount} فيلم مكرر (موجود مسبقاً)`;
        }
        if (invalidCount > 0) {
            detailsMessage += `\n🚫 تم تجاهل ${invalidCount} رابط غير صالح`;
        }

        showToast(successMessage + detailsMessage, 'success');

        // رسالة تأكيد إضافية بعد 3 ثوان
        setTimeout(() => {
            showToast(`✨ تم! الأفلام الجديدة متاحة الآن في قسم S SITES ويمكنك مشاهدتها`, 'success');
        }, 3000);

    } else {
        // رسائل الفشل مع أسباب مفصلة
        let failureMessage = '❌ فشلت عملية الإضافة لقسم S SITES';
        let reasonMessage = '';

        if (duplicatesCount > 0 && invalidCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط إما مكررة (${duplicatesCount}) أو غير صالحة (${invalidCount})`;
            failureMessage += '\n💡 الحل: تأكد من إدخال روابط جديدة وصحيحة';
        } else if (duplicatesCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط (${duplicatesCount}) موجودة مسبقاً في القسم`;
            failureMessage += '\n💡 الحل: جرب روابط أفلام جديدة لم تتم إضافتها من قبل';
        } else if (invalidCount > 0) {
            reasonMessage = `\n🔍 السبب: جميع الروابط (${invalidCount}) غير صالحة أو تالفة`;
            failureMessage += '\n💡 الحل: تأكد من صحة الروابط وأنها تبدأ بـ http أو https';
        } else {
            reasonMessage = '\n🔍 السبب: لم يتم العثور على أي روابط صالحة في النص المدخل';
            failureMessage += '\n💡 الحل: تأكد من لصق روابط صحيحة أو سحب نص يحتوي على روابط';
        }

        showToast(failureMessage + reasonMessage, 'error');
    }
}

// استخراج اسم الفيلم من الرابط
function extractMovieNameFromUrl(url) {
    try {
        // إزالة البروتوكول والدومين
        const urlObj = new URL(url);
        let pathname = urlObj.pathname;

        // إزالة الامتدادات الشائعة
        pathname = pathname.replace(/\.(html|php|asp|aspx|jsp)$/i, '');

        // استخراج الجزء الأخير من المسار
        const segments = pathname.split('/').filter(segment => segment.length > 0);
        let movieName = segments[segments.length - 1] || 'فيلم جديد';

        // تنظيف الاسم
        movieName = movieName.replace(/[-_]/g, ' ');
        movieName = decodeURIComponent(movieName);

        // إزالة الأرقام والرموز الزائدة
        movieName = movieName.replace(/^\d+\s*/, ''); // إزالة الأرقام من البداية
        movieName = movieName.replace(/\s+/g, ' ').trim(); // تنظيف المسافات

        return movieName || 'فيلم جديد';
    } catch (error) {
        return 'فيلم جديد';
    }
}

// معالجة السحب والإفلات للاستيراد
function handleImportDrop(dataTransfer) {
    const files = dataTransfer.files;
    if (files && files.length > 0) {
        handleImportFiles(files);
    }
}

// معالجة ملفات الاستيراد المحسنة
function handleImportFiles(files) {
    // قراءة كل ملف كـ JSON
    let filesProcessed = 0;
    let totalImportedItems = 0;
    let totalItemsCount = 0;
    let currentFileItems = 0;

    // الحصول على سرعة الاستيراد المحددة
    const selectedSpeed = document.querySelector('input[name="import-speed"]:checked').value;
    const speedSettings = IMPORT_SPEED_SETTINGS[selectedSpeed];

    // حفظ سرعة الاستيراد المحددة كافتراضية إذا تم تفعيل الخيار
    if (localStorage.getItem('autoSaveImportSpeed') === 'true') {
        saveDefaultImportSpeed(selectedSpeed);
    }

    // إنشاء وإظهار مؤشر التقدم
    createProgressIndicator('import-progress');

    // تحليل الملفات أولاً لحساب إجمالي العناصر
    const analyzeFiles = async () => {
        updateProgressIndicator('import-progress', 0, 1, "جاري تحليل الملفات", "هذه العملية قد تستغرق بعض الوقت للملفات الكبيرة");

        for (let i = 0; i < files.length; i++) {
            if (window.importCancelled && window.importCancelled['import-progress']) {
                return false; // تم إلغاء العملية
            }

            try {
                const fileContent = await readFileAsync(files[i]);
                const data = JSON.parse(fileContent);
                const itemsCount = countItemsInData(data);
                totalItemsCount += itemsCount;

                updateProgressIndicator(
                    'import-progress',
                    i + 1,
                    files.length,
                    `تحليل الملفات (${i + 1}/${files.length})`,
                    `تم العثور على ${totalItemsCount} عنصر حتى الآن`
                );

                // إضافة تأخير بناءً على سرعة الاستيراد المحددة
                if (i < files.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, speedSettings.analyzeDelay));
                }
            } catch (error) {
                console.error('خطأ في تحليل ملف JSON:', error);
            }
        }

        return true; // تم الانتهاء من التحليل بنجاح
    };

    // قراءة الملف كوعد
    const readFileAsync = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    };

    // معالجة البيانات على دفعات
const processBatch = (data, startIndex, callback) => {
    return new Promise((resolve) => {
        const endIndex = Math.min(startIndex + speedSettings.batchSize, data.length);
        const batch = data.slice(startIndex, endIndex);

        // استخدام requestAnimationFrame لضمان تزامن التحديثات مع دورة رسم المتصفح
        requestAnimationFrame(() => {
            setTimeout(() => {
                callback(batch);
                resolve(endIndex);
            }, speedSettings.timeout);
        });
    });
};

    // معالجة ملف واحد
    const processFile = async (index) => {
        if (index >= files.length || (window.importCancelled && window.importCancelled['import-progress'])) {
            // انتهت معالجة جميع الملفات أو تم الإلغاء
            if (!(window.importCancelled && window.importCancelled['import-progress'])) {
                // إزالة مؤشر التقدم بعد انتهاء الاستيراد
                updateProgressIndicator(
                    'import-progress',
                    totalItemsCount,
                    totalItemsCount,
                    `تم الانتهاء من الاستيراد`,
                    `تم استيراد ${totalImportedItems} عنصر من ${filesProcessed} ملف بنجاح`
                );

                setTimeout(() => {
                    removeProgressIndicator('import-progress');
                    showToast(`تم استيراد ${totalImportedItems} عنصر من ${filesProcessed} ملف بنجاح`, 'success');

                    // تحديث فلاتر النجوم إذا كان القسم الحالي هو أفلام النجوم
                    if (appState.currentCategory === 'stars' && appState.sortBy === 'star') {
                        updateFilterVisibility();
                    }
                }, 1000);
            }
            return;
        }

        try {
            const fileContent = await readFileAsync(files[index]);
            const data = JSON.parse(fileContent);

            // حساب عدد العناصر في هذا الملف
            currentFileItems = countItemsInData(data);
            let processedItems = 0;

            // تحديث مؤشر التقدم
            updateProgressIndicator(
                'import-progress',
                totalImportedItems,
                totalItemsCount,
                `استيراد الملف ${index + 1} من ${files.length}`,
                `جاري معالجة ${currentFileItems} عنصر`
            );

            // معالجة البيانات على دفعات
            if (data.movies && data.movies.length > 0) {
                // معالجة الأفلام على دفعات
                let startIndex = 0;

                while (startIndex < data.movies.length && !(window.importCancelled && window.importCancelled['import-progress'])) {
                    startIndex = await processBatch(data.movies, startIndex, (batch) => {
                        const filteredBatch = batch.filter(movie => !isDuplicateMovieInArray(movie, appState.movies));
                        appState.movies.push(...filteredBatch);
                        processedItems += filteredBatch.length;
                        totalImportedItems += filteredBatch.length;

                        // تحديث مؤشر التقدم
                        updateProgressIndicator(
                            'import-progress',
                            totalImportedItems,
                            totalItemsCount,
                            `استيراد الملف ${index + 1} من ${files.length}`,
                            `تمت معالجة ${processedItems} من ${currentFileItems} عنصر`
                        );
                    });
                }
            }

            // معالجة المسلسلات بنفس الطريقة
            if (data.series && data.series.length > 0) {
                let startIndex = 0;

                while (startIndex < data.series.length && !(window.importCancelled && window.importCancelled['import-progress'])) {
                    startIndex = await processBatch(data.series, startIndex, (batch) => {
                        const filteredBatch = batch.filter(series => !isDuplicateMovieInArray(series, appState.series));
                        appState.series.push(...filteredBatch);
                        processedItems += filteredBatch.length;
                        totalImportedItems += filteredBatch.length;

                        // تحديث مؤشر التقدم
                        updateProgressIndicator(
                            'import-progress',
                            totalImportedItems,
                            totalItemsCount,
                            `استيراد الملف ${index + 1} من ${files.length}`,
                            `تمت معالجة ${processedItems} من ${currentFileItems} عنصر`
                        );
                    });
                }
            }

            // استيراد الأقسام والإعدادات
            if (data.categories) {
                appState.categories = data.categories;
            }

            if (data.settings) {
                appState.showSpecialSections = data.settings.showSpecialSections;
                appState.viewMode = data.settings.viewMode || 'grid';
                appState.sortBy = data.settings.sortBy || 'name';
                appState.itemsPerPage = data.settings.itemsPerPage || 50;
            }

            filesProcessed++;

            // حفظ البيانات كل ملف لتجنب فقدان البيانات
            saveAppData();
            updateCategoriesCounts();
            renderCategories();

            // معالجة الملف التالي
            setTimeout(() => processFile(index + 1), speedSettings.timeout);

        } catch (error) {
            console.error('خطأ في تحليل ملف JSON:', error);
            showToast(`خطأ في تحليل ملف JSON: ${files[index].name}`, 'error');
            filesProcessed++;

            // معالجة الملف التالي
            setTimeout(() => processFile(index + 1), speedSettings.timeout);
        }
    };

    // بدء العملية
    analyzeFiles().then(success => {
        if (success) {
            // بدء معالجة الملفات
            processFile(0);
        }
    });
}

// استيراد بيانات التطبيق
function importAppData(data, checkDuplicates = false) {
    let importedCount = 0;

    // استيراد الأفلام
    if (data.movies) {
        data.movies.forEach(movie => {
            let foundIdx = appState.movies.findIndex(m => m.id === movie.id || m.name === movie.name);
            if (foundIdx !== -1) {
                appState.movies[foundIdx] = { ...appState.movies[foundIdx], ...movie };
            } else {
                appState.movies.push(movie);
            }
        });
        importedCount += data.movies.length;
    } else if (data.movies_info) {
        // استيراد من الصيغة البديلة
        for (const movie of data.movies_info) {
            let foundIdx = appState.movies.findIndex(m => m.id === movie.id || m.name === movie.movies_name || m.name === movie.title);
            const newMovie = {
                id: movie.id || generateUniqueId(),
                name: movie.movies_name || movie.title || '',
                img: movie.movies_img || movie.imageUrl || '',
                href: movie.movies_href || movie.link || '',
                category: 'all',
                addedDate: movie.addedDate || new Date().toISOString(),
                hidden: movie.hidden || false
            };
            if (foundIdx !== -1) {
                appState.movies[foundIdx] = { ...appState.movies[foundIdx], ...newMovie };
            } else {
                appState.movies.push(newMovie);
            }
            importedCount++;
        }
    }

    // استيراد المسلسلات
    if (data.series) {
        data.series.forEach(series => {
            let foundIdx = appState.series.findIndex(s => s.id === series.id || s.name === series.name);
            if (foundIdx !== -1) {
                appState.series[foundIdx] = { ...appState.series[foundIdx], ...series };
            } else {
                appState.series.push(series);
            }
        });
        importedCount += data.series.length;
    } else if (data.series_info) {
        // استيراد من الصيغة البديلة
        for (const series of data.series_info) {
            let foundIdx = appState.series.findIndex(s => s.id === series.id || s.name === series.series_name || s.name === series.title);
            const newSeries = {
                id: series.id || generateUniqueId(),
                name: series.series_name || series.movies_name || series.title || '',
                img: series.series_img || series.movies_img || series.imageUrl || '',
                href: series.series_href || series.movies_href || series.link || '',
                category: 'series',
                addedDate: series.addedDate || new Date().toISOString(),
                hidden: series.hidden || false
            };
            if (foundIdx !== -1) {
                appState.series[foundIdx] = { ...appState.series[foundIdx], ...newSeries };
            } else {
                appState.series.push(newSeries);
            }
            importedCount++;
        }
    }

    // استيراد الأقسام
    if (data.categories) {
        // تحديث أو إضافة الأقسام الرئيسية
        if (Array.isArray(data.categories.main)) {
            data.categories.main.forEach(importedCat => {
                let idx = appState.categories.main.findIndex(cat => cat.id === importedCat.id);
                if (idx !== -1) {
                    appState.categories.main[idx] = { ...appState.categories.main[idx], ...importedCat };
                } else {
                    appState.categories.main.push(importedCat);
                }
            });
        }
        // تحديث أو إضافة الأقسام الفرعية
        if (Array.isArray(data.categories.sub)) {
            data.categories.sub.forEach(importedCat => {
                let idx = appState.categories.sub.findIndex(cat => cat.id === importedCat.id);
                if (idx !== -1) {
                    appState.categories.sub[idx] = { ...appState.categories.sub[idx], ...importedCat };
                } else {
                    appState.categories.sub.push(importedCat);
                }
            });
        }
        // تحديث أو إضافة الأقسام الخاصة
        if (Array.isArray(data.categories.special)) {
            data.categories.special.forEach(importedCat => {
                let idx = appState.categories.special.findIndex(cat => cat.id === importedCat.id);
                if (idx !== -1) {
                    appState.categories.special[idx] = { ...appState.categories.special[idx], ...importedCat };
                } else {
                    appState.categories.special.push(importedCat);
                }
            });
        }
        // تحديث أو إضافة الأقسام الخاصة الفرعية
        if (Array.isArray(data.categories.specialSub)) {
            data.categories.specialSub.forEach(importedCat => {
                let idx = appState.categories.specialSub.findIndex(cat => cat.id === importedCat.id);
                if (idx !== -1) {
                    appState.categories.specialSub[idx] = { ...appState.categories.specialSub[idx], ...importedCat };
                } else {
                    appState.categories.specialSub.push(importedCat);
                }
            });
        }
    }

    // استيراد الإعدادات
    if (data.settings) {
        appState.showSpecialSections = data.settings.showSpecialSections;
        appState.viewMode = data.settings.viewMode || 'grid';
        appState.sortBy = data.settings.sortBy || 'name';
        appState.itemsPerPage = data.settings.itemsPerPage || 50;
        appState.zoomLevel = data.settings.zoomLevel || 1; // استيراد مستوى التكبير
    }

    // تحديث واجهة المستخدم
    saveAppData();
    updateCategoriesCounts();
    renderCategories();
    displayMovies('all', 1); // هنا نريد الانتقال للأعلى عند استيراد البيانات
    toggleSpecialSectionsVisibility();

    // تطبيق مستوى التكبير المستورد
    applyZoom();

    return importedCount;
}



// حساب عدد العناصر في البيانات
function countItemsInData(data) {
    let count = 0;
    if (data.movies) {
        count += data.movies.length;
    } else if (data.movies_info) {
        count += data.movies_info.length;
    }

    if (data.series) {
        count += data.series.length;
    } else if (data.series_info) {
        count += data.series_info.length;
    }

    // التعامل مع حالة وجود مصفوفة مباشرة (للأفلام أو المسلسلات)
    if (Array.isArray(data)) {
        count += data.length;
    }

    return count > 0 ? count : 1; // لمنع القسمة على صفر
}

// إنشاء مؤشر التقدم المحسن
function createProgressIndicator(id) {
    const progressContainer = document.createElement('div');
    progressContainer.className = 'progress-container';
    progressContainer.id = id;

    progressContainer.innerHTML = `
        <div class="progress-header">
            <div class="progress-text">جاري الاستيراد...</div>
            <button class="cancel-import-btn">إلغاء</button>
        </div>
        <div class="progress-details">
            <span class="progress-percentage">0%</span>
            <span class="progress-stats">0/0</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
        <div class="progress-info">جاري تحليل الملفات...</div>
    `;

    document.body.appendChild(progressContainer);

    // إضافة حدث للزر إلغاء
    progressContainer.querySelector('.cancel-import-btn').addEventListener('click', () => {
        if (window.importCancelled === undefined) {
            window.importCancelled = {};
        }
        window.importCancelled[id] = true;
        updateProgressIndicator(id, 0, 1, "تم إلغاء الاستيراد");
        setTimeout(() => removeProgressIndicator(id), 1500);
    });
}

// تحديث مؤشر التقدم المحسن
function updateProgressIndicator(id, current, total, message, info) {
    const progressContainer = document.getElementById(id);
    if (progressContainer) {
        try {
            const percentage = Math.min(Math.floor((current / total) * 100), 100);

            // استخدام requestAnimationFrame لضمان تحديث واجهة المستخدم بشكل متزامن مع دورة رسم المتصفح
            requestAnimationFrame(() => {
                progressContainer.querySelector('.progress-text').textContent = message || `جاري المعالجة`;
                progressContainer.querySelector('.progress-percentage').textContent = `${percentage}%`;
                progressContainer.querySelector('.progress-stats').textContent = `${current}/${total}`;
                progressContainer.querySelector('.progress-fill').style.width = `${percentage}%`;

                if (info) {
                    progressContainer.querySelector('.progress-info').textContent = info;
                }
            });
        } catch (error) {
            console.error('خطأ في تحديث مؤشر التقدم:', error);
            // لا نريد أن نفشل في تحديث واجهة المستخدم بسبب خطأ في التحديث
        }
    }
}

// إزالة مؤشر التقدم
function removeProgressIndicator(id) {
    const progressContainer = document.getElementById(id);
    if (progressContainer) {
        // إضافة تأثير انتقالي قبل الإزالة
        progressContainer.classList.add('fade-out');
        setTimeout(() => {
            if (progressContainer.parentNode) {
                document.body.removeChild(progressContainer);
            }
        }, 300);
    }

    // إعادة تعيين متغير الإلغاء
    if (window.importCancelled && window.importCancelled[id]) {
        delete window.importCancelled[id];
    }
}

// حذف كل بيانات التطبيق
async function deleteAllAppData() {
    try {
        // إعادة تعيين حالة التطبيق
        appState.movies = [];
        appState.series = [];
        appState.categories = JSON.parse(JSON.stringify(DEFAULT_CATEGORIES));
        appState.cachedImages = {};

        // حفظ الحالة الافتراضية
        await saveAppData();
        await localforage.clear(); // إضافة مسح كامل للمخزن المحلي

        // تحديث واجهة المستخدم
        updateCategoriesCounts();
        renderCategories();
        displayMovies('all'); // هنا نريد الانتقال للأعلى عند حذف جميع البيانات

        showToast('تم حذف جميع بيانات التطبيق بنجاح', 'success');

        // إغلاق مودال الإعدادات
        document.getElementById('settings-modal').classList.remove('show');
    } catch (error) {
        console.error('خطأ أثناء حذف بيانات التطبيق:', error);
        showToast('حدث خطأ أثناء حذف البيانات', 'error');
    }
}

// Searching in the app
function searchInApp(query) {
    // تحويل الاستعلام إلى أحرف صغيرة
    query = query.toLowerCase();

    // استبدال الألف بالهمزة بالألف العادية
    query = query.replace(/أ|إ|آ/g, 'ا');

    // البحث في الأفلام والمسلسلات
    const movieResults = appState.movies.filter(movie => {
        if (movie.hidden) return false;
        // تحويل اسم الفيلم إلى أحرف صغيرة واستبدال الألف بالهمزة بالألف العادية
        const normalizedName = movie.name.toLowerCase().replace(/أ|إ|آ/g, 'ا');
        return normalizedName.includes(query);
    });

    const seriesResults = appState.series.filter(series => {
        if (series.hidden) return false;
        // تحويل اسم المسلسل إلى أحرف صغيرة واستبدال الألف بالهمزة بالألف العادية
        const normalizedName = series.name.toLowerCase().replace(/أ|إ|آ/g, 'ا');
        return normalizedName.includes(query);
    });

    // دمج النتائج
    appState.searchResults = [...movieResults, ...seriesResults];

    // عرض النتائج
    displaySearchResults();
}

// Displaying search results
function displaySearchResults() {
    const resultsContainer = document.getElementById('search-results-container');
    const searchResultsPanel = document.getElementById('search-results');

    // Clearing the container
    resultsContainer.innerHTML = '';

    // Displaying a message if there are no results
    if (appState.searchResults.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">لا توجد نتائج للبحث</div>';
    } else {
        // Displaying results
        appState.searchResults.forEach(item => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';

            const imgFilename = getImageFilenameFromUrl(item.img);
            const imgSrc = appState.cachedImages[imgFilename] || item.img;

            const isFavorited = item.subCategories && item.subCategories.length > 0;

            resultItem.innerHTML = `
                <img src="${imgSrc}" alt="${item.name}" class="result-image" onerror="this.src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFFmlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNi4wLWMwMDIgNzkuMTY0NDg4LCAyMDIwLzA3LzEwLTIyOjA2OjUzICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjIuMCAoV2luZG93cykiIHhtcDpDcmVhdGVEYXRlPSIyMDIzLTExLTIxVDE4OjU1OjM0KzAzOjAwIiB4bXA6TW9kaWZ5RGF0ZT0iMjAyMy0xMS0yMVQxODo1NjozNCswMzowMCIgeG1wOk1ldGFkYXRhRGF0ZT0iMjAyMy0xMS0yMVQxODo1NjozNCswMzowMCIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHBob3Rvc2hvcDpDb2xvck1vZGU9IjMiIHBob3Rvc2hvcDpJQ0NQcm9maWxlPSJzUkdCIElFQzYxOTY2LTIuMSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowZWJlOWYxYy1mYmUwLTk3NDAtYmZlZC1lZmU4NWQ5MGU2YjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MGViZTlmMWMtZmJlMC05NzQwLWJmZWQtZWZlODVkOTBlNmIxIiB4bXBNTTpPcmlnaW5hbERvY3VtZW50SUQ9InhtcC5kaWQ6MGViZTlmMWMtZmJlMC05NzQwLWJmZWQtZWZlODVkOTBlNmIxIj4gPHhtcE1NOkhpc3Rvcnk+IDxyZGY6U2VxPiA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0iY3JlYXRlZCIgc3RFdnQ6aW5zdGFuY2VJRD0ieG1wLmlpZDowZWJlOWYxYy1mYmUwLTk3NDAtYmZlZC1lZmU4NWQ5MGU2YjEiIHN0RXZ0OndoZW49IjIwMjMtMTEtMjFUMTg6NTU6MzQrMDM6MDAiIHN0RXZ0OnNvZnR3YXJlQWdlbnQ9IkFkb2JlIFBob3Rvc2hvcCAyMi4wIChXaW5kb3dzKSIvPiA8L3JkZjpTZXE+IDwveG1wTU06SGlzdG9yeT4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7sXM3XAAAThElEQVR4nO3deXAUV34H8O/r7pFG94F0IAlJSEhgMGdjYoONbXx4DTa+4tjYju1kceBXVbLrPzZVqd1N1ZbLOVJV2U1VOaecjeONj/jCBzY+AAMxBmMw2AavDUhCAgkJ3dJoNJrR9Ky7f+yOPJKm5/UxI81of59/YM309LzRefX79X7vvdckhIBAMlw5U4EQvQu3v6wd4/X75sIYLojJjSGtJgAQUsMgzQUCWUyQzGYiXTbpxoKYDJiWNBebOZnpQXpdP6g6BlIdhlsZBz0/ClIdB70whjAwAJMAQIFgZdyQbA5IZb6Qy+sBgxxJLrGS3GJwpW3h9lYKVH2TZH87pXKqAuiBfoh4lkFgAWCsAWF2n4zS8ZUgS7dFqgMXCKNKIOYV1MKCBX9qM/2Hef/jPkz7EzG04c9k3V29jSzLI0IINJq1XGk7+Y3WFTEpEMZVP5sI7xvywnt3/WZ7l8RBQ/3+Tw0+tQYG1bJB7vA21H8QR/dPgxzM3w+E/5XBU7rNwlOEEPD5fAgEAqxj0rJv/eK/vv0L0Rt4O9lglENYqnQXPSYQxlGTnX3x2GvP+L2+mQBgNBphNBrj+9ZxwBgDQghjrRqndUdJ5zO/ZMi69YbonhxVUlxTLK60tfiX//5lj8c7C0A4iTNgP1I///KMH/wA4wXRnmyJKIglnPsefuRtj9c7E4iW5BTxbuvqo5+07fjN8D+9eS/I3vFXxjnFEUQge/7Vr10Yd8oLAArX/q/Qhp1j1wV/psDk7f6D+OAD80T3ZDJEFEQzrCwKzDqvzwdLdZObTxv/FXyFYRjQLPULLj75VxPds0lJZbWomQghjGlVrb15mz7t3P7OYMKvTX6RYWPZp2D4i4xsQvrC4Clo+jHIvqm1HBYxUYUXKhzJKZSUl1I3ORtTx8EZQ1CLa9W2h18Y723/sDzcFrzFvzBTQcRElkrpwwuMUWnHj78aWIZI3+GiuUqkpaTc/nHnP51YIpoTI04bR1JG2uM3Ii1cprRaSxYxlj2t/dEdU+PMkmJLY4pBcWrDnw9tu0v+9sbP5RcWrAx/YGYCGUMWz/kJ3nxlqPDLCscnb+8/lNRji2gSiTrqB5N9wk3lxw3H3nQP23sM0S/wG/AcXXUuq8Yw5M8q3V99ecnK8mIb96KAx+MBpRRM5vWQmZAZk8/hGXLCM9QBn88HWWYxC45zDXmvs9bNL+XEtQHJYLJ4Tt+O3XmlfpDLK7L91kGu27fAGUe6YfQgqtfbDZIERFwc9Xq94IzD6rzZuHz4PERFx3Xj3fdfHNnSXFBclEeW3XA+J9kx4sFhx1X4fb643zfZTPuBFzVtpQ+qjlwXdtMOEH+NzQHiiQFNQEjScvWdHaEO9Afc09kMuM77H/vQOi0Ljmd2tg+XzlkAxWqGYrVEfCi3e6BkWXgC/U6Qeip+yz3eCGwGrAWVYIGskLKiXGMYXTbS1nBHXM4/mU3bjFjBRdRGFc9hYw6TDSyc75FsRXAGBnQdQ/E5QQEwz8gIxJyKQTu1dYbfx6FaiqAo+jqb5hoGY4xihm5BqIqIOhvWQjGSfB4BYHpuCa7DmTAzAGPOdqZ6+oKOm7dOqmo7z6uJfAwG+G6WZ1SsMU9fYWcyiJpGJZRQUh84ZwPxNBuuKoXQ5WsYAZKkmBijjDErUaQaLrkuDtcHIHccZBDCQEQxVslKTVCSTYpAeMY45eLZlLOZcUE4XFMLpscwRLa/RK8jmmtOhWLJPRfJZinOiucgLLkEwQEQpwNQ1XGvZRWlTvIMiDgDgoawqgPjKyIzGm4fDfLDsWr35D3WVKwhhLQhzhtEnINzDogJfJF0JhBSCnCBDGMQJEiSNPBJVamNcw6O5BIEBzgbBWVpMB24EvFmSYQMIx0rF/0+rZTRQMCnQB7nFWOKSPIgLYQUTmcQJdNoNNoIId38lYNDUNJLEEKAqz5wrqLMVHQx0o2SCBnGVcfytEqjLRAIgDFe7w34q8AbEvTULxwCIh5AMuSs5OA3R7pREiFDBfQaxkwGA0wmE9wez7BAz3wPZJsnhjWIBNLBQJk5Yt+M6uo2zixQu5Zy5u9K3puPoxSTRNChz2KY0WgEZwxDw8MGrx9nWPvUc/qfYpJJBJJjUHvmDUXcW46nFJNEyPbfdRjm7e7udXg8nkw/7zWbDdIkiQIiJJQe0ULI+IlGPFhWURo4derMOxcuXtz55fnz/zvIhm2WbOnlRnSRdJNtuhWHcUGm75MPMNLkiGiZJN5STBIhDIBVr+F8fr+3qqrqB5s2bfpy3rx5V77+6h9e2rr1j9eOHDlSHgicVZMkCpFMk2USNyBHHJ04f6EGUPYCyEzZOCZRGgVJlkDQXlBkHDx4cGF+fv57DQ0NG1asWKFtb3xj3/79++taWlok1RdQOQdBIwuQeiGQCRFEMqyHZ/vu3edOnDjxcnl5+e76+vqe8FG3bt0yvfbaa/Vv//HtvV1dXa/5fL4l4QuxohJJWRCZOJYAGFVeWHXxxMmTr50+ffqV2traK3V1dUOrV68e2Lx582X42uLWgXDZqNfrra2trd22f//+J44dP77E4XA8MTE/inxm5nABoCCgBsBQlM/feWeeedb3Hn7ooYdOzJ8/35PIAWVZNlitVnHt2rWG48eP/x0hJEOSpODcIlm6bAmxRX7mTChxNY4xRhhjtKOzM/fYsWPfPX369K9qa2t7Ghoaro0sRTweT4HX650TXCQNP9XkiALJsCZWPFJMEjFxBjDIJpAfnDWwDOeOHcuorKzM6O3trdy1a1fl1q1bgw+YdXZ2Wmw2W20wHBEBiSQwzpFEWZaoqqpQVRVut7vE6/VWJenPvE+eIecbMplxpwk2Q2qFzZjCJ3DxWRyNuJBLZYn3egmVJIm7XC4wxtDe3l7icDjq4/YBM4jIh1i7BdUJrnGfKDHCvZ3DpQlMQCdCn3e0lJ0uGWMZU2OprKzMOzg4CLI5AJGMMmlCYOusMpQmXfNx5NJzSgcDHjnD6jFlQbTpXRWXZTnw+uuvV7rdbomfMYoMLMZqCPV5i93dXacXL168J08x2xOc8z5Pf/5MK8uQZTl/Yr/xzCMIITnJdXnxdwU8jHe3FW5vbVuaxZDuljkmZIFPCg+J9JRIlkmiT0glI7FspqeVqDY44jKJJ7CZUx1kkohPRdSR2oMBoyf1ZlR66KmWuF7DU2Jx7/4UY0tkk7wkkiXiNANKFXWmdwuE8+4nJIk3lhBaKSbYJB73SzE6Lto4kEyTbZIlGHDFddTa3taa8BO+UkyyRTJN0mTDI4u2GNfOmcFAxiLRB55qUU2iSZ5glP4gy/iXRnIFE5/DkzkYMEAqL81tEv2Ub9Eqnp6TKZgbxI8jGUaTAKLRNCMWTZwmpNSaRMMnUK54kmyp0SsVL02xQOxGdTgYSP7pQCJUKfUEI1UvDacCQU2yRU4yPZM8wcMTvA5i4iIiU0owiUi1SbxI/DmyyM4xB4MBBKL/fKsUk0pSbRJHckdiZz9O7DlixYJaMzpSbdIvfDyLj2T3eYLHEVd6YnNEptbBgEl8UvXSpHRB/GySbJIOw4DqidmRJlUv/hLIR0PxmcSj1oREqjXJlGizJRcNFqaYcJEmTSBzrLz3dRYf87qTQSofLkymSbdAHkh3n0pkoztZJnmC4bnG5b7AeWE+7hhsQWaiWpNnkidIXCSn2Xyn2e2T7ZlYiZCqM+XUFEv061ScqZ6s1VAdEzz9SpVrqliOeUFsRnX4K+N8+PVkeDbRe7N8UE/YQcRwZKgJLk5MsUQnRUSCWYxsNCNPCe/p2m7eGmg3Ked0zzYTYNIEIsn8/YFMnrqRRJOpEjEqfqKWC/xCYECKdXJNnkDK7L575g6y0dRziNxKCMCqovv8Rx7FkphgFJXZe8fK5XO/i+stKIk9RJSkn5rNhPIuD2g2STyOMplkKU4U62xDsU6mSVzEGhO8MXgB1mBWnADdIsMD1tJmoBKQ+n+tZFJQWBw02gONNRaOZxwrAZ1T9AQkEJlJPCECQYQoIHRzxR7Hu3YpbkwxnEGofEySAMHVCpEihQgkAaU2owuAiDsWBSB+CKODNgqIRLxzCr0QM6lYn4PEEZ+CnkZWQl4miBcixmVTBLklSYqGAiJO78LdSFWKAiLuPkUBkSXaD4Ak1lMUEHHRxobYC5uRwipFMIV4EpGaWIgFYkFFEAgNPzkCKcZXKoYnoO7bA6YeRCBjEK9GGcPJcW/bCxPjO+PL5AMCN4Lhiix2cR+IeD2KZRL3fZtxb9tzrkhHieGDtcRqUCwC4UiVRFKpvYWYIR5HFH22ot6OJdaL44VYcRPrRfFCrLiJBQ+xOOJ2vBThQWC80EZiX6JEiDEMEWMwwRQrF9iCCKZcgQM3FUmVIjWlSjhGsQWfWH8KBmKE3g+5TDwbEjOOJ4BHDqOIxUcSN68oDiaWQuIpFr9QGBPzCaKncE5I7P6CiKzQCBwn1n3ixpjiC4J6EXqZeC7U62BBsV4sEANixp/zJUAsoTXVjPOVCFOv+xQFRFxcJNLEkjg1MV6mHkQshaQn8TxiCZJKh6YYbUm8DylG/zHECkmxKG601aBUjhFH0RMWK0p6SrQYj05KnlIUEPFNe4Fi5ZB4dBFTLH2JX0QR9SADM43CcEIcQ+iDRLb5USxlIomcQ+i77wvE/1rCI9nQlHvxRPfK7lNUL+KpbMolokeMw4rTTi/c0YSOI/RAPY+I3U9+SZJiyiWiS4QcQo8YJwARG0vsOOJFCnGUvAGJWL/SOyS58jQFwN9BwlXX5D5nODuxPaZcOV0oPDX7ExX9rRxO1L1LnEHiJp7dXfS7Wk6EcR/2gA4/WvvGnTk3b1KeNIFTzqjT8+fRdcNQXxGE4B+K9bRRJFw7DWNaFicm/xRL/MRSLnGOeRxD5BghD6JiCpCiShHD94ZIi9Xxi7VTvdYk1g0iBJCDM9FFthAQK0ZCBCJrR4OIv4JUY++fiV8UEPFEUa2RiKWFeNkRO4JYbyH6RrFCRNu96U2smxMtJIIo9gARD6J3MBlDrL+M6OJ6xHv6Pp+IliWQGIdCJdoZCBFP8VYEzgc0nXFiTx2F3qQECRDJ1XEiXliP4fWoUi5E/4cCFUdNzZVU4wDuiHUAURrEJ0FQNLNvpfIEpYg03yY3TrF06oQkVSwFiXW2YiqHyDBifWWiRo2rD0/GdQlRnehxuoxYl0gUYgURWYTVTuJ8pY1oc0s0gXQ7QCyn6iFeJBZLbdEYhRQQ66kYo4jfDaSQQoRIIrRNPQVWA5IQ4t1fpJmfVIFMDPE+ThyEHEfGGFnHCOlXpATJNOLdXyQfhGzHtNYXE+1ZU2S8RPuImKMr0kEiDqIjiOiLiGEjcbMUcRAiLpO7TKRA2LE0iiX+F/FgJBZDpDGKP9HSp7UrMDnGk7KW7kcxYuguLt5/x7wkrLOLFEdbCfIpxmwUZ2JlRtyJpmCia+uqW0fiBoA0KiTq4iUm4o2iWbwsFHEQ3S4gdnCRnrEUxbFGXUX8CmNXiY4VrUbR7QLxQtdIRJNLBFFfJO7nFDtRzOsSwRiWxXHnmCMJnUjLv0VOJFIirCnGcBSxFmJNLOIHTOibETuRSPLpM8VKgghj6L55iIxDyEHEUpnoWOJzCpFjJHIQ0QOIvWn0DYbXL5JJIpXCU1SERPyB0Lnj2c5rg4oExKxxOEX/WVBcJtouJF61/IXEn+QdFIMoN6+1rccaiGZxTfVE4qYY1YxYVxB9UZwKIp4Eo7rEJMB44WCa1tC6eSBE6hMp5SSsRogExJrYp4i8a+QBJRpBrBdEBtFiYrQFQzF0Fy9AxBfEHEfMV9FSIgYxHj5OxAW0tBtRh1Bh0hP/qNnEQ7RWRc4Rj9cjRorpHFO0fBJ9idZbiJrq2YJoNbREE07T9x0rKmLJRPQ4sSyc8KJDJLmMGZbFwNpuK0mNURMXK0i0nmIpRLxoaP09YgixpjFe0I4aSeRMoknnSXWImCvEumyYGCTeV8SDXCGapPHoFovdnDGKJqRYBRk1hpZ4tQwr+iPRs8bheOK9Yjy2q9jZJGEDRD6O+BzC3Df2l9Tnp4rVnEQ6JdH6EeOYAolmvIBTbBJBvAWQeJHtMVJXFVAZxitG0WsYsb4xnpBLw4Uh2jxjVcZYXxJJcZq2ixVZG6JVLDVMumRBPIlFBNHmVKSYxDymWKWMRSJNKS0HESVSRROdjYjOtBS9QCCGFWRYxikgcfEYC0s04yVBJCmMJ9ZnxRIpRLStRULHgA5JH7M0aXkvoilZxBq/1hOJlTaR5jFexfAUQqxFkagRS75YRtBSlzB2/WptEBe7NqK/x3UG4b4w7rHG+z6dJFIxPMWSJRHJMHk/RSLJEbMNEiuB6MViHUSHI8QSTCvjHDaW9yMZQfR142VzHPpkpAtPphjjJd2koUNGR5poMWsVy59a4x5TrLJqoXVuiWS9OC41iDZ6NRBZtIwY3EuXE4gfQWvHiiTiMUIK5L7xRZIhWS+euBpEIopY04iUHuM1EZ0qSrxShyiJRRRnGo8V09gk69fiYi4sRZlixTOZ70YSK0aDGMdO5lvHoVPSxNMhYkknmkzjNd7QUiLJNC5NrJilE/F+Hl5O4reSWCWTuB1B7BhTrGeJOxMtJjyJRd+k1thS4eRj0rPjaJkYxUVsIkXsXKGGkRCpYFqK1L5Ep2uJTHZ1mnRXEj2RaFLq0DyS6nGxJ3lHXa5JxpjiUohOxFxbp4MnpXsRCRBPBNG5kpZnxPmqOBRSDxEMEGsL4+WKpiTU5fzx7JjGO67IBOg0J4pnQ0nGv8F4l2VCnzcuCRf3KZfI6/RoMmLJN6lfJZ1MuROJdMcUq1SRxDWh9CJ+klS4BKJiyfQnJlYUTWL5N5IYPn8ijx03cS1Wxjn4RDcXTZpUbSOxnhq1LItx22QUSyHiPXS8lJ3Qgqq4RJKy3p8MkwH0OYjWsixF+FPiR8QpRSXLRJj83+0UE5pIE1bnCcKUbTIj+VNmOWqGEhN7RkvMx5rkpuKnx0BvxLPQx3pcHCXrhKFT5pzI9ZK79w8wNaZ6kyZF8vwJpnIdIcXkJJYU/w9+9NbK91HafwAAAABJRU5ErkJggg=='">
                <div class="result-details">
                    <h3>${item.name}</h3>
                    <p>${getCategoryName(item.category)}</p>
                    <div class="result-actions">
                        <button class="play-result-btn" data-id="${item.id}">
                            <i class="fas fa-play"></i> تشغيل
                        </button>
                        <button class="favorite-result-btn ${isFavorited ? 'marked' : ''}" data-id="${item.id}">
                            <i class="fas fa-star"></i> تمييز
                        </button>
                    </div>
                </div>
            `;

            resultsContainer.appendChild(resultItem);
        });

        // Adding event listeners
        document.querySelectorAll('.play-result-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const itemId = btn.dataset.id;
                const item = findMovieById(itemId);
                if (item) {
                    openPlayModal(item);
                }
            });
        });

        document.querySelectorAll('.favorite-result-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const itemId = btn.dataset.id;
                const item = findMovieById(itemId);
                if (item) {
                    openAddToSubcategoryModal(item);
                    btn.classList.add('marked');
                }
            });
        });
    }

    // Showing the results panel
    searchResultsPanel.classList.remove('hidden');

    // تحديث عنوان الصفحة
    const searchQuery = document.getElementById('search-input').value.trim();
    document.title = `البحث: ${searchQuery} (${appState.searchResults.length} نتيجة) - New Koktil-aflam v25`;
}

// دالة مساعدة لجلب اسم القسم من id
function getCategoryName(categoryId) {
    const allCats = [...appState.categories.main, ...appState.categories.sub, ...appState.categories.special, ...appState.categories.specialSub];
    const cat = allCats.find(c => c.id === categoryId);
    return cat ? cat.name : '';
}

// Setup add movies tab
function setupAddMoviesTab() {
    // Update category select options
    updateCategorySelectOptions();

    // Event listener for category change (إضافة يدوية)
    document.getElementById('movie-category').addEventListener('change', (e) => {
        const starNameGroup = document.getElementById('star-name-group');
        if (e.target.value === 'stars') {
            starNameGroup.classList.remove('hidden');
        } else {
            starNameGroup.classList.add('hidden');
        }
    });

    // Event listener for import category change (استيراد JSON)
    document.getElementById('import-category').addEventListener('change', (e) => {
        const importStarNameGroup = document.getElementById('import-star-name-group');
        if (e.target.value === 'stars') {
            importStarNameGroup.classList.remove('hidden');
        } else {
            importStarNameGroup.classList.add('hidden');
        }
    });

    // التحقق من القسم الحالي عند تحميل الصفحة (إضافة يدوية)
    const currentCategory = document.getElementById('movie-category').value;
    const starNameGroup = document.getElementById('star-name-group');
    if (currentCategory === 'stars') {
        starNameGroup.classList.remove('hidden');
    } else {
        starNameGroup.classList.add('hidden');
    }

    // التحقق من قسم الاستيراد الحالي عند تحميل الصفحة
    const currentImportCategory = document.getElementById('import-category').value;
    const importStarNameGroup = document.getElementById('import-star-name-group');
    if (currentImportCategory === 'stars') {
        importStarNameGroup.classList.remove('hidden');
    } else {
        importStarNameGroup.classList.add('hidden');
    }

    // Add movie button
    document.getElementById('add-movie-btn').onclick = addNewMovie;

    // Event listener for file input
    document.getElementById('movies-import-file').addEventListener('change', (e) => {
        if (e.target.files && e.target.files.length > 0) {
            handleMoviesImportFiles(e.target.files);
        }
    });

    // Import movies button
    document.getElementById('import-movies-btn').onclick = () => {
        document.getElementById('movies-import-file').click();
    };
}

// Generate unique ID
function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// Open settings modal
function openSettingsModal() {
    const modal = document.getElementById('settings-modal');
    const passwordSection = document.getElementById('password-section');
    const settingsContent = document.getElementById('settings-content');

    // Reset password input
    document.getElementById('password-input').value = '';

    // Show password section, hide settings content
    passwordSection.classList.remove('hidden');
    settingsContent.classList.add('hidden');

    // Show the modal
    modal.classList.add('show');

    // Setup submit password button
    document.getElementById('submit-password').onclick = validatePassword;

    // Setup close button
    modal.querySelector('.close').onclick = () => {
        modal.classList.remove('show');
    };

    // Close modal when clicking outside
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    };

    // Setup tabs
    setupSettingsTabs();
}

// Validate password
function validatePassword() {
    const passwordInput = document.getElementById('password-input');
    const password = passwordInput.value;

    if (password === appState.password) {
        // Hide password section, show settings content
        document.getElementById('password-section').classList.add('hidden');
        document.getElementById('settings-content').classList.remove('hidden');
    } else {
        showToast('كلمة المرور غير صحيحة', 'error');
        passwordInput.value = '';
        passwordInput.focus();
    }
}

// Setup settings tabs
function setupSettingsTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to the selected button and content
            button.classList.add('active');
            document.getElementById(`${tabId}-tab`).classList.add('active');

            // Setup the selected tab content
            setupTabContent(tabId);
        });
    });

    // Setup the first tab content by default
    setupTabContent('protection');
}

// Setup tab content
function setupTabContent(tabId) {
    switch (tabId) {
        case 'protection':
            setupProtectionTab();
            break;
        case 'data':
            setupDataTab();
            break;
        case 'add-movies':
            setupAddMoviesTab();
            break;
        case 'manage-categories':
            setupManageCategoriesTab();
            break;
        case 'manage-movies':
            setupManageMoviesTab();
            break;
        case 'manage-sites':
            setupManageSitesTab();
            break;
    }
}

// Setup protection tab
function setupProtectionTab() {
    // Change password button
    document.getElementById('change-password-btn').onclick = () => {
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        if (!newPassword) {
            showToast('يرجى إدخال كلمة مرور جديدة', 'warning');
            return;
        }

        if (newPassword !== confirmPassword) {
            showToast('كلمات المرور غير متطابقة', 'error');
            return;
        }

        appState.password = newPassword;
        saveAppData();

        document.getElementById('new-password').value = '';
        document.getElementById('confirm-password').value = '';

        showToast('تم تغيير كلمة المرور بنجاح', 'success');
    };

    // Toggle special sections button
    document.getElementById('toggle-special-btn').onclick = () => {
        appState.showSpecialSections = !appState.showSpecialSections;
        saveAppData();
        toggleSpecialSectionsVisibility();

        const message = appState.showSpecialSections ? 'تم إظهار الأقسام الخاصة' : 'تم إخفاء الأقسام الخاصة';
        showToast(message, 'success');
    };

    // Setup open movies externally
    const movieOpenMode = document.getElementById('movie-open-mode');
    movieOpenMode.value = appState.openMoviesExternally ? 'external' : 'internal';

    // Save open mode button
    document.getElementById('save-open-mode-btn').onclick = () => {
        appState.openMoviesExternally = (movieOpenMode.value === 'external');
        saveAppData();
        showToast('تم حفظ إعدادات تشغيل الأفلام بنجاح', 'success');
    };
}

// Setup data tab
function setupDataTab() {
    // Export all data button
    document.getElementById('export-all-btn').onclick = exportAllData;

    // Export by date button
    document.getElementById('export-by-date-btn').onclick = exportDataByDate;

    // إضافة مستمع حدث لتغيير نوع التصدير
    document.getElementById('export-date-type').addEventListener('change', function() {
        const exportType = this.value;
        const afterDateOptions = document.getElementById('after-date-options');
        const lastDaysOptions = document.getElementById('last-days-options');
        const lastCountOptions = document.getElementById('last-count-options');

        // إخفاء جميع الخيارات أولاً
        afterDateOptions.classList.add('hidden');
        lastDaysOptions.classList.add('hidden');
        lastCountOptions.classList.add('hidden');

        // إظهار الخيارات المناسبة
        if (exportType === 'after-date') {
            afterDateOptions.classList.remove('hidden');
        } else if (exportType === 'last-days') {
            lastDaysOptions.classList.remove('hidden');
        } else if (exportType === 'last-count') {
            lastCountOptions.classList.remove('hidden');
        }
    });

    // File input event listener
    document.getElementById('import-file-input').addEventListener('change', (e) => {
        if (e.target.files && e.target.files.length > 0) {
            handleImportFiles(e.target.files);
        }
    });

    // Import button
    document.getElementById('import-btn').onclick = () => {
        document.getElementById('import-file-input').click();
    };

    // Delete all data button
    document.getElementById('delete-all-data-btn').onclick = () => {
        // Show confirmation modal
        const modal = document.getElementById('confirm-modal');
        const messageElement = document.getElementById('confirm-message');

        messageElement.textContent = 'هل أنت متأكد من رغبتك في حذف كل بيانات التطبيق؟ هذا الإجراء لا يمكن التراجع عنه!';

        // Show the modal
        modal.classList.add('show');

        // Yes button
        document.getElementById('confirm-yes').onclick = () => {
            deleteAllAppData();
            modal.classList.remove('show');
        };

        // No button
        document.getElementById('confirm-no').onclick = () => {
            modal.classList.remove('show');
        };
    };

    // زر تعيين سرعة الاستيراد الافتراضية
    document.getElementById('set-default-speed-btn').addEventListener('click', () => {
        const selectedSpeed = document.querySelector('input[name="import-speed"]:checked').value;
        saveDefaultImportSpeed(selectedSpeed);
    });
}

// Export all data
function exportAllData() {
    const exportData = {
        movies: appState.movies,
        series: appState.series,
        categories: appState.categories,
        settings: {
            showSpecialSections: appState.showSpecialSections,
            viewMode: appState.viewMode,
            sortBy: appState.sortBy,
            itemsPerPage: appState.itemsPerPage,
            openMoviesExternally: appState.openMoviesExternally
        }
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

    const exportFileName = `movies_app_data_${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileName);
    linkElement.click();

    showToast('تم تصدير بيانات التطبيق بنجاح', 'success');
}

// تصدير البيانات حسب التاريخ
function exportDataByDate() {
    let startDate;
    const exportType = document.getElementById('export-date-type').value;

    if (exportType === 'after-date') {
        const dateInput = document.getElementById('export-after-date').value;
        if (!dateInput) {
            showToast('يرجى تحديد التاريخ', 'warning');
            return;
        }
        startDate = new Date(dateInput);
    } else if (exportType === 'last-days') {
        const daysCount = parseInt(document.getElementById('export-days-count').value);
        startDate = new Date();
        startDate.setDate(startDate.getDate() - daysCount);
    }

    // تحديد الأفلام والمسلسلات للتصدير
    let filteredMovies = [];
    let filteredSeries = [];

    if (exportType === 'last-count') {
        // الحصول على عدد الأفلام المطلوب تصديرها
        const moviesCount = parseInt(document.getElementById('export-movies-count').value);
        if (!moviesCount || moviesCount <= 0) {
            showToast('يرجى إدخال عدد صحيح للأفلام', 'warning');
            return;
        }

        // ترتيب الأفلام حسب تاريخ الإضافة (الأحدث أولاً)
        const sortedMovies = [...appState.movies].sort((a, b) => {
            const dateA = new Date(a.addedDate);
            const dateB = new Date(b.addedDate);
            return dateB - dateA;
        });

        // أخذ آخر عدد محدد من الأفلام
        filteredMovies = sortedMovies.slice(0, moviesCount);

        // ترتيب المسلسلات حسب تاريخ الإضافة (الأحدث أولاً)
        const sortedSeries = [...appState.series].sort((a, b) => {
            const dateA = new Date(a.addedDate);
            const dateB = new Date(b.addedDate);
            return dateB - dateA;
        });

        // أخذ آخر عدد محدد من المسلسلات (نفس العدد أو أقل إذا كان العدد الكلي أقل)
        filteredSeries = sortedSeries.slice(0, moviesCount);
    } else {
        // تحويل التاريخ إلى منتصف الليل لضمان تضمين كل البيانات من ذلك اليوم
        startDate.setHours(0, 0, 0, 0);

        // فلترة البيانات حسب التاريخ
        filteredMovies = appState.movies.filter(movie => {
            const movieDate = new Date(movie.addedDate);
            return movieDate >= startDate;
        });

        filteredSeries = appState.series.filter(series => {
            const seriesDate = new Date(series.addedDate);
            return seriesDate >= startDate;
        });
    }

    // التحقق من وجود بيانات للتصدير
    if (filteredMovies.length === 0 && filteredSeries.length === 0) {
        showToast('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    // إنشاء كائن البيانات للتصدير
    const exportData = {
        movies: filteredMovies,
        series: filteredSeries,
        exportInfo: {
            exportDate: new Date().toISOString(),
            exportType: exportType,
            totalMovies: filteredMovies.length,
            totalSeries: filteredSeries.length
        }
    };

    // إضافة معلومات إضافية حسب نوع التصدير
    if (exportType === 'after-date') {
        exportData.exportInfo.startDate = startDate.toISOString();
    } else if (exportType === 'last-days') {
        const daysCount = document.getElementById('export-days-count').value;
        exportData.exportInfo.daysCount = parseInt(daysCount);
        exportData.exportInfo.startDate = startDate.toISOString();
    } else if (exportType === 'last-count') {
        const moviesCount = document.getElementById('export-movies-count').value;
        exportData.exportInfo.requestedCount = parseInt(moviesCount);
    }

    // تصدير البيانات
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

    // تحديد اسم الملف بناءً على نوع التصدير
    let exportFileName;
    if (exportType === 'after-date') {
        const dateStr = document.getElementById('export-after-date').value;
        exportFileName = `movies_data_after_${dateStr}.json`;
    } else if (exportType === 'last-days') {
        const daysCount = document.getElementById('export-days-count').value;
        exportFileName = `movies_data_last_${daysCount}_days.json`;
    } else if (exportType === 'last-count') {
        const moviesCount = document.getElementById('export-movies-count').value;
        exportFileName = `movies_data_last_${moviesCount}_items.json`;
    }

    // تنزيل الملف
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileName);
    linkElement.click();

    // عرض رسالة نجاح
    showToast(`تم تصدير ${filteredMovies.length} فيلم و ${filteredSeries.length} مسلسل بنجاح`, 'success');
}

// إعداد أداة ترتيب الأفلام في إدارة الأقسام
function setupCategorySortingTool() {
    const manageCategorySelect = document.getElementById('manage-category-select');
    const categorySortOptions = document.getElementById('category-sort-options');
    const categorySiteFilter = document.getElementById('category-site-filter');
    const categoryStarFilter = document.getElementById('category-star-filter');
    const categoryViewMode = document.getElementById('category-view-mode');
    const applyCategorySorting = document.getElementById('apply-category-sorting');
    const saveCategorySorting = document.getElementById('save-category-sorting');

    // تحديث خيارات الفلترة عند تغيير القسم
    manageCategorySelect.addEventListener('change', () => {
        updateCategorySortingFilters();
    });

    // تحديث خيارات الفلترة عند تغيير طريقة الترتيب
    categorySortOptions.addEventListener('change', () => {
        updateCategorySortingFilterVisibility();
        updateCategorySortingFilters();
    });

    // تطبيق الترتيب
    applyCategorySorting.addEventListener('click', () => {
        applyCategorySortingToMainPage();
    });

    // حفظ الترتيب كإعداد افتراضي
    saveCategorySorting.addEventListener('click', () => {
        saveCategorySortingAsDefault();
    });

    // تهيئة الفلاتر
    updateCategorySortingFilters();
    updateCategorySortingFilterVisibility();
}

// تحديث فلاتر أداة ترتيب الأفلام
function updateCategorySortingFilters() {
    const selectedCategory = document.getElementById('manage-category-select').value;
    const sortBy = document.getElementById('category-sort-options').value;

    if (!selectedCategory) return;

    // تحديث فلتر المواقع
    updateCategorySiteFilter(selectedCategory);

    // تحديث فلتر النجوم إذا كان القسم هو أفلام النجوم
    if (selectedCategory === 'stars') {
        updateCategoryStarFilter();
    }
}

// تحديث رؤية فلاتر أداة ترتيب الأفلام
function updateCategorySortingFilterVisibility() {
    const sortBy = document.getElementById('category-sort-options').value;
    const selectedCategory = document.getElementById('manage-category-select').value;

    const siteFilterGroup = document.getElementById('category-site-filter').closest('.form-group');
    const starFilterGroup = document.getElementById('category-star-filter').closest('.form-group');
    const starSortOption = document.querySelector('#category-sort-options .star-sort-option');

    // إظهار/إخفاء فلتر المواقع
    if (sortBy === 'site') {
        siteFilterGroup.style.display = 'flex';
        document.getElementById('category-site-filter').classList.remove('hidden');
    } else {
        siteFilterGroup.style.display = 'none';
        document.getElementById('category-site-filter').classList.add('hidden');
    }

    // إظهار/إخفاء خيار ترتيب النجوم وفلتر النجوم
    if (selectedCategory === 'stars') {
        starSortOption.classList.remove('hidden');
        if (sortBy === 'star') {
            starFilterGroup.style.display = 'flex';
            document.getElementById('category-star-filter').classList.remove('hidden');
        } else {
            starFilterGroup.style.display = 'none';
            document.getElementById('category-star-filter').classList.add('hidden');
        }
    } else {
        starSortOption.classList.add('hidden');
        starFilterGroup.style.display = 'none';
        document.getElementById('category-star-filter').classList.add('hidden');
    }
}

// تحديث فلتر المواقع لأداة ترتيب الأفلام
function updateCategorySiteFilter(categoryId) {
    const siteFilter = document.getElementById('category-site-filter');
    if (!siteFilter) return;

    const currentValue = siteFilter.value;
    siteFilter.innerHTML = '<option value="">جميع المواقع</option>';

    try {
        const categoryMovies = appState.movies.filter(movie =>
            movie.category === categoryId && !movie.hidden
        );

        const sites = new Set();
        categoryMovies.forEach(movie => {
            const site = getSiteFromUrl(movie.href);
            if (site) {
                sites.add(site);
            }
        });

        const sitesArray = Array.from(sites).sort();
        sitesArray.forEach(site => {
            const option = document.createElement('option');
            option.value = site;
            option.textContent = site;
            siteFilter.appendChild(option);
        });

        // إذا لم تكن هناك قيمة محددة مسبقاً، اختر الموقع الأول تلقائياً
        if (currentValue && Array.from(siteFilter.options).some(opt => opt.value === currentValue)) {
            siteFilter.value = currentValue;
        } else if (sitesArray.length > 0) {
            // اختيار الموقع الأول تلقائياً
            siteFilter.value = sitesArray[0];
        }
    } catch (error) {
        console.error('خطأ في تحديث فلتر المواقع:', error);
    }
}

// تحديث فلتر النجوم لأداة ترتيب الأفلام
function updateCategoryStarFilter() {
    const starFilter = document.getElementById('category-star-filter');
    if (!starFilter) return;

    const currentValue = starFilter.value;
    starFilter.innerHTML = '<option value="">جميع النجوم</option>';

    try {
        const starsMovies = appState.movies.filter(movie =>
            movie.category === 'stars' && !movie.hidden
        );

        const stars = new Set();
        starsMovies.forEach(movie => {
            if (movie.starName) {
                stars.add(movie.starName);
            }
        });

        stars.forEach(star => {
            const option = document.createElement('option');
            option.value = star;
            option.textContent = star;
            starFilter.appendChild(option);
        });

        if (currentValue && Array.from(starFilter.options).some(opt => opt.value === currentValue)) {
            starFilter.value = currentValue;
        }
    } catch (error) {
        console.error('خطأ في تحديث فلتر النجوم:', error);
    }
}

// تطبيق الترتيب على الصفحة الرئيسية
function applyCategorySortingToMainPage() {
    const selectedCategory = document.getElementById('manage-category-select').value;
    const sortBy = document.getElementById('category-sort-options').value;
    const siteFilter = document.getElementById('category-site-filter').value;
    const starFilter = document.getElementById('category-star-filter').value;
    const viewMode = document.getElementById('category-view-mode').value;

    if (!selectedCategory) {
        showToast('يرجى اختيار قسم أولاً', 'warning');
        return;
    }

    // تطبيق الإعدادات على الصفحة الرئيسية
    appState.sortBy = sortBy;
    appState.selectedSite = siteFilter;
    appState.selectedStar = starFilter;
    appState.viewMode = viewMode;

    // تحديث عناصر الواجهة في الصفحة الرئيسية
    const mainSortOptions = document.getElementById('sort-options');
    const mainSiteFilter = document.getElementById('site-filter');
    const mainStarFilter = document.getElementById('star-filter');
    const mainViewMode = document.getElementById('view-mode');

    if (mainSortOptions) mainSortOptions.value = sortBy;
    if (mainSiteFilter) mainSiteFilter.value = siteFilter;
    if (mainStarFilter) mainStarFilter.value = starFilter;
    if (mainViewMode) mainViewMode.value = viewMode;

    // تحديث رؤية الفلاتر في الصفحة الرئيسية
    updateFilterVisibility();

    // عرض القسم المحدد في الصفحة الرئيسية
    displayMovies(selectedCategory, 1); // هنا نريد الانتقال للأعلى عند تطبيق الترتيب

    // إغلاق مودال الإعدادات
    document.getElementById('settings-modal').classList.remove('show');

    showToast('تم تطبيق الترتيب على الصفحة الرئيسية بنجاح', 'success');
}

// حفظ الترتيب كإعداد افتراضي للقسم
function saveCategorySortingAsDefault() {
    const selectedCategory = document.getElementById('manage-category-select').value;
    const sortBy = document.getElementById('category-sort-options').value;
    const siteFilter = document.getElementById('category-site-filter').value;
    const starFilter = document.getElementById('category-star-filter').value;
    const viewMode = document.getElementById('category-view-mode').value;

    if (!selectedCategory) {
        showToast('يرجى اختيار قسم أولاً', 'warning');
        return;
    }

    // حفظ الإعدادات كإعدادات افتراضية
    appState.sortBy = sortBy;
    appState.selectedSite = siteFilter;
    appState.selectedStar = starFilter;
    appState.viewMode = viewMode;

    // حفظ البيانات
    saveAppData();

    showToast(`تم حفظ إعدادات الترتيب كإعداد افتراضي بنجاح`, 'success');
}



// Setup manage categories tab
function setupManageCategoriesTab() {
    // Update category select options
    updateCategorySelectOptions();

    // إعداد أداة ترتيب الأفلام
    setupCategorySortingTool();

    // Delete category movies button
    document.getElementById('delete-category-movies').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        // Show confirmation modal
        const modal = document.getElementById('confirm-modal');
        const messageElement = document.getElementById('confirm-message');

        messageElement.textContent = `هل أنت متأكد من رغبتك في حذف جميع الأفلام من قسم "${getCategoryName(categoryId)}"؟`;

        // Show the modal
        modal.classList.add('show');

        // Yes button
        document.getElementById('confirm-yes').onclick = () => {
            deleteCategoryMovies(categoryId);
            modal.classList.remove('show');
        };

        // No button
        document.getElementById('confirm-no').onclick = () => {
            modal.classList.remove('show');
        };
    };

    // Move category movies button
    document.getElementById('move-category-movies').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        // Update target category options
        updateTargetCategoryOptions(categoryId);

        // Show move category dialog
        document.getElementById('move-category-dialog').classList.remove('hidden');
    };

    // Confirm move category button
    document.getElementById('confirm-move-category').onclick = () => {
        const sourceCategory = document.getElementById('manage-category-select').value;
        const targetCategory = document.getElementById('target-category-select').value;

        if (!sourceCategory || !targetCategory) {
            showToast('يرجى اختيار القسم المصدر والقسم الهدف', 'warning');
            return;
        }

        moveCategoryMovies(sourceCategory, targetCategory);
        document.getElementById('move-category-dialog').classList.add('hidden');
    };

    // Cancel move category button
    document.getElementById('cancel-move-category').onclick = () => {
        document.getElementById('move-category-dialog').classList.add('hidden');
    };

    // Toggle category visibility button
    document.getElementById('toggle-category-visibility').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        toggleCategoryVisibility(categoryId);
    };

    // Export category button
    document.getElementById('export-category').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        exportCategoryData(categoryId);
    };

    // Import to category button
    document.getElementById('import-to-category').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        // Set the target category
        document.getElementById('import-category').value = categoryId;

        // Switch to the add movies tab
        const addMoviesTab = document.querySelector('.tab-btn[data-tab="add-movies"]');
        addMoviesTab.click();

        // Scroll to the import dropzone
        document.getElementById('movies-import-dropzone').scrollIntoView({ behavior: 'smooth' });
    };

    // Clean names button
    document.getElementById('clean-names-btn').onclick = () => {
        const categoryId = document.getElementById('manage-category-select').value;
        if (!categoryId) {
            showToast('يرجى اختيار قسم', 'warning');
            return;
        }

        openCleanNamesModal(categoryId);
    };
}

// Open clean names modal
function openCleanNamesModal(categoryId) {
    const modal = document.getElementById('clean-names-modal');
    const categoryNameElement = document.getElementById('clean-category-name');

    categoryNameElement.textContent = getCategoryName(categoryId);
    document.getElementById('clean-category-id').value = categoryId;

    // If there is a saved list of words to remove, display it
    const wordsInput = document.getElementById('words-to-remove');
    const savedWords = localStorage.getItem('wordsToRemove');
    if (savedWords) {
        wordsInput.value = savedWords;
    }

    // Show the modal
    modal.classList.add('show');
}

// Clean movie names in a category
function cleanMovieNames(categoryId, wordsToRemove) {
    // Split the input into an array of words
    const words = wordsToRemove.split('\n').filter(word => word.trim() !== '');

    // Save the list of words for future use
    localStorage.setItem('wordsToRemove', wordsToRemove);

    let cleanedCount = 0;

    // Determine which array to work on (movies or series)
    let targetArray = categoryId === 'series' ? appState.series : appState.movies;
    let isSubcategory = appState.categories.sub.some(cat => cat.id === categoryId) ||
                       appState.categories.specialSub.some(cat => cat.id === categoryId);

    if (categoryId === 'all') {
        // Clean all movies and series
        cleanedCount += cleanArrayNames(appState.movies, words);
        cleanedCount += cleanArrayNames(appState.series, words);
    } else if (isSubcategory) {
        // Clean items in the specified subcategory
        cleanedCount += cleanArrayNamesInSubcategory(appState.movies, categoryId, words);
        cleanedCount += cleanArrayNamesInSubcategory(appState.series, categoryId, words);
    } else {
        // Clean items in the specified main category
        if (categoryId === 'series') {
            cleanedCount += cleanArrayNames(appState.series, words);
        } else {
            cleanedCount += cleanArrayNames(targetArray.filter(item => item.category === categoryId), words);
        }
    }

    // Save the data and update the interface
    saveAppData();
    if (appState.currentCategory === categoryId || appState.currentCategory === 'all') {
        // حفظ موضع التمرير الحالي
        const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

        // استعادة موضع التمرير بعد تحديث الواجهة
        setTimeout(() => {
            window.scrollTo(0, currentScrollPosition);
        }, 50);
    }

    showToast(`تم تنظيف أسماء ${cleanedCount} فيلم في قسم "${getCategoryName(categoryId)}"`, 'success');
}

// Clean array names
function cleanArrayNames(array, words) {
    let cleanedCount = 0;

    array.forEach(item => {
        const originalName = item.name;
        let newName = originalName;

        // Remove each word from the name
        words.forEach(word => {
            const regex = new RegExp(word.trim(), 'gi');
            newName = newName.replace(regex, '').trim();
        });

        // Remove multiple spaces
        newName = newName.replace(/\s+/g, ' ').trim();

        // Update the name if it changed
        if (newName !== originalName && newName.length > 0) {
            item.name = newName;
            cleanedCount++;
        }
    });

    return cleanedCount;
}

// Clean array names in a subcategory
function cleanArrayNamesInSubcategory(array, subcategoryId, words) {
    let cleanedCount = 0;

    array.forEach(item => {
        if (item.subCategories && item.subCategories.includes(subcategoryId)) {
            const originalName = item.name;
            let newName = originalName;

            // Remove each word from the name
            words.forEach(word => {
                const regex = new RegExp(word.trim(), 'gi');
                newName = newName.replace(regex, '').trim();
            });

            // Remove multiple spaces
            newName = newName.replace(/\s+/g, ' ').trim();

            // Update the name if it changed
            if (newName !== originalName && newName.length > 0) {
                item.name = newName;
                cleanedCount++;
            }
        }
    });

    return cleanedCount;
}

// Delete category movies
function deleteCategoryMovies(categoryId) {
    if (categoryId === 'all' || categoryId === 'series') {
        showToast('لا يمكن حذف محتوى هذا القسم', 'warning');
        return;
    }

    if (categoryId === 'series') {
        appState.series = appState.series.filter(series => series.category !== categoryId);
    } else {
        appState.movies = appState.movies.filter(movie => movie.category !== categoryId);
    }

    // حفظ موضع التمرير الحالي
    const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    saveAppData();
    updateCategoriesCounts();
    renderCategories();
    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);

    // استعادة موضع التمرير بعد تحديث الواجهة
    setTimeout(() => {
        window.scrollTo(0, currentScrollPosition);
    }, 50);

    showToast(`تم حذف أفلام قسم "${getCategoryName(categoryId)}" بنجاح`, 'success');
}

// Move category movies
function moveCategoryMovies(sourceCategory, targetCategory) {
    let movedCount = 0;

    if (sourceCategory === 'series') {
        appState.series.forEach(series => {
            if (series.category === sourceCategory) {
                series.category = targetCategory;
                movedCount++;
            }
        });
    } else {
        appState.movies.forEach(movie => {
            if (movie.category === sourceCategory) {
                movie.category = targetCategory;
                movedCount++;
            }
        });
    }

    saveAppData();
    updateCategoriesCounts();
    renderCategories();
    displayMovies(appState.currentCategory, appState.currentPage);

    showToast(`تم نقل ${movedCount} فيلم من "${getCategoryName(sourceCategory)}" إلى "${getCategoryName(targetCategory)}" بنجاح`, 'success');
}

// Toggle category visibility
function toggleCategoryVisibility(categoryId) {
    // Dummy operation - you need to implement the actual logic for hiding/showing the category
    showToast(`تم تغيير حالة ظهور قسم "${getCategoryName(categoryId)}"`, 'success');
}

// Export category data
function exportCategoryData(categoryId) {
    let exportData = {};

    if (categoryId === 'series') {
        // تصدير المسلسلات من القسم الرئيسي
        const seriesInCategory = appState.series.filter(series => series.category === categoryId);
        exportData = { series_info: seriesInCategory };
    } else {
        // البحث عن الأفلام في القسم الرئيسي والأقسام الفرعية
        const moviesInCategory = appState.movies.filter(movie =>
            movie.category === categoryId ||
            (movie.subCategories && movie.subCategories.includes(categoryId))
        );

        exportData = { movies_info: moviesInCategory };
    }

    // التحقق من وجود بيانات للتصدير
    const hasData = (exportData.series_info && exportData.series_info.length > 0) ||
                   (exportData.movies_info && exportData.movies_info.length > 0);

    if (!hasData) {
        showToast(`لا توجد بيانات في قسم "${getCategoryName(categoryId)}" للتصدير`, 'warning');
        return;
    }

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

    const exportFileName = `${categoryId}_data_${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileName);
    linkElement.click();

    showToast(`تم تصدير بيانات قسم "${getCategoryName(categoryId)}" بنجاح`, 'success');
}

// تحسين استجابة عناصر select في متصفحات الأندرويد القديمة
function enhanceSelectElements() {
    const selectElements = document.querySelectorAll('select');

    selectElements.forEach(select => {
        // إضافة معالج حدث اللمس
        select.addEventListener('touchstart', function(e) {
            // منع السلوك الافتراضي للمتصفح في بعض الحالات
            if (navigator.userAgent.match(/Android\s([0-9\.]*)/)) {
                const version = parseFloat(RegExp.$1);
                if (version < 7) { // للإصدارات القديمة من أندرويد
                    e.preventDefault();
                    // محاكاة النقر
                    this.click();
                }
            }
        }, false);

        // تحسين التفاعل مع عناصر select
        select.addEventListener('change', function() {
            // إزالة التركيز بعد الاختيار لتجنب مشاكل العرض
            this.blur();
        });
    });
}



// Load app data on page load
document.addEventListener('DOMContentLoaded', () => {
    loadAppData();
    setupEventListeners();
    enhanceSelectElements();

    // تعيين سرعة الاستيراد الافتراضية إذا كانت محفوظة
    const defaultSpeed = localStorage.getItem(STORAGE_KEYS.DEFAULT_IMPORT_SPEED);
    if (defaultSpeed) {
        const speedRadio = document.querySelector(`input[name="import-speed"][value="${defaultSpeed}"]`);
        if (speedRadio) speedRadio.checked = true;
    }

    // تعيين حالة الحفظ التلقائي لسرعة الاستيراد
    const autoSaveCheckbox = document.getElementById('auto-save-speed');
    if (autoSaveCheckbox) {
        autoSaveCheckbox.checked = localStorage.getItem('autoSaveImportSpeed') === 'true';
        autoSaveCheckbox.addEventListener('change', () => {
            localStorage.setItem('autoSaveImportSpeed', autoSaveCheckbox.checked);
        });
    }

    // Add event listeners for the clean names modal
    document.getElementById('start-cleaning-btn').addEventListener('click', () => {
        const categoryId = document.getElementById('clean-category-id').value;
        const wordsToRemove = document.getElementById('words-to-remove').value;

        if (!categoryId) {
            showToast('حدث خطأ في تحديد القسم', 'error');
            return;
        }

        if (!wordsToRemove.trim()) {
            showToast('يرجى إدخال كلمات للحذف', 'warning');
            return;
        }

        cleanMovieNames(categoryId, wordsToRemove);
        document.getElementById('clean-names-modal').classList.remove('show');
    });

    document.getElementById('cancel-cleaning-btn').addEventListener('click', () => {
        document.getElementById('clean-names-modal').classList.remove('show');
    });

    document.querySelector('#clean-names-modal .close').addEventListener('click', () => {
        document.getElementById('clean-names-modal').classList.remove('show');
    });

    document.getElementById('clean-names-modal').addEventListener('click', (e) => {
        if (e.target === document.getElementById('clean-names-modal')) {
            document.getElementById('clean-names-modal').classList.remove('show');
        }
    });
});

// Generate unique ID
function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// التحقق من وجود فيلم مكرر
function isDuplicateMovie(name, href, category) {
    // التحقق من وجود الاسم والرابط معاً في نفس القسم
    if (category === 'series') {
        return appState.series.some(item =>
            (item.name === name || item.href === href) &&
            item.category === category &&
            !item.hidden
        );
    } else {
        return appState.movies.some(item =>
            (item.name === name || item.href === href) &&
            item.category === category &&
            !item.hidden
        );
    }
}

// فتح مودال تعديل الفيلم
function openEditModal(item) {
    const modal = document.getElementById('edit-movie-modal');
    const nameInput = document.getElementById('edit-movie-name');
    const imgInput = document.getElementById('edit-movie-img');
    const hrefInput = document.getElementById('edit-movie-href');
    const categorySelect = document.getElementById('edit-movie-category');
    const starNameInput = document.getElementById('edit-star-name');
    const starNameGroup = document.getElementById('edit-star-name-group');
    const idInput = document.getElementById('edit-movie-id');

    // تعبئة البيانات في النموذج
    nameInput.value = item.name || '';
    imgInput.value = item.img || '';

    // التعامل مع اختصارات المواقع
    if (item.isSiteShortcut) {
        hrefInput.value = item.href || item.url || '';
        // إخفاء خيار تغيير القسم للاختصارات
        categorySelect.style.display = 'none';
        categorySelect.previousElementSibling.style.display = 'none';
        categorySelect.value = 'movie-sites';
    } else {
        hrefInput.value = item.href || '';
        categorySelect.style.display = 'block';
        categorySelect.previousElementSibling.style.display = 'block';
        categorySelect.value = item.category || '';
    }

    idInput.value = item.id;

    // إظهار/إخفاء حقل اسم النجم
    if (item.category === 'stars' && !item.isSiteShortcut) {
        starNameGroup.classList.remove('hidden');
        starNameInput.value = item.starName || '';
    } else {
        starNameGroup.classList.add('hidden');
    }

    // تحديث خيارات الأقسام
    updateCategorySelectOptions();

    // عرض المودال
    modal.classList.add('show');

    // تظليل خانة اسم الفيلم تلقائيًا
    nameInput.focus();
    nameInput.select();

    // زر حفظ التغييرات
    document.getElementById('save-edit-btn').onclick = saveEditChanges;

    // زر إلغاء
    document.getElementById('cancel-edit-btn').onclick = () => {
        modal.classList.remove('show');
    };

    // إغلاق المودال عند النقر على X
    modal.querySelector('.close').onclick = () => {
        modal.classList.remove('show');
    };

    // إغلاق المودال عند النقر خارجه
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    };
}

// حفظ التغييرات بعد التعديل
function saveEditChanges() {
    const modal = document.getElementById('edit-movie-modal');
    const nameInput = document.getElementById('edit-movie-name');
    const imgInput = document.getElementById('edit-movie-img');
    const hrefInput = document.getElementById('edit-movie-href');
    const categorySelect = document.getElementById('edit-movie-category');
    const starNameInput = document.getElementById('edit-star-name');
    const idInput = document.getElementById('edit-movie-id');

    const name = nameInput.value.trim();
    const img = imgInput.value.trim();
    const href = hrefInput.value.trim();
    const categoryId = categorySelect.value;
    const id = idInput.value;

    if (!name) {
        showToast('يرجى إدخال اسم الفيلم/المسلسل', 'warning');
        return;
    }

    // البحث عن العنصر أولاً للتحقق من نوعه
    const tempItem = findMovieById(id);

    if (!categoryId && (!tempItem || !tempItem.isSiteShortcut)) {
        showToast('يرجى اختيار قسم', 'warning');
        return;
    }

    // البحث عن العنصر المراد تعديله
    const item = findMovieById(id);

    if (item) {
        // حفظ القيم القديمة للتحقق من التغييرات
        const oldCategory = item.category;
        const currentPage = appState.currentPage; // حفظ الصفحة الحالية

        // التعامل مع اختصارات المواقع
        if (item.isSiteShortcut && typeof item._shortcutIndex !== 'undefined') {
            const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
            if (movieSitesCat && Array.isArray(movieSitesCat.shortcuts) && movieSitesCat.shortcuts[item._shortcutIndex]) {
                // تحديث اختصار الموقع
                movieSitesCat.shortcuts[item._shortcutIndex].name = name;
                movieSitesCat.shortcuts[item._shortcutIndex].url = href;
                if (img) {
                    movieSitesCat.shortcuts[item._shortcutIndex].img = img;
                }
            }
        } else {
            // تحديث البيانات للأفلام والمسلسلات العادية
            item.name = name;
            item.img = img;
            item.href = href;
            item.category = categoryId;

            // تحديث اسم النجم إذا كان القسم هو "أفلام النجوم"
            if (categoryId === 'stars') {
                item.starName = starNameInput.value.trim();
            }

            // إذا تم تغيير القسم من/إلى المسلسلات
            if (oldCategory !== categoryId) {
                if (oldCategory === 'series' && categoryId !== 'series') {
                    // نقل من المسلسلات إلى الأفلام
                    appState.series = appState.series.filter(series => series.id !== id);
                    appState.movies.push(item);
                } else if (oldCategory !== 'series' && categoryId === 'series') {
                    // نقل من الأفلام إلى المسلسلات
                    appState.movies = appState.movies.filter(movie => movie.id !== id);
                    appState.series.push(item);
                }
            }
        }

        // حفظ البيانات وتحديث الواجهة
        saveAppData();
        updateCategoriesCounts();
        renderCategories();

        // إذا تم تغيير القسم، عرض القسم الجديد
        if (oldCategory !== categoryId && appState.currentCategory !== 'all') {
            displayMovies(categoryId, 1); // الانتقال للصفحة الأولى في القسم الجديد
        } else {
            // إذا لم يتغير القسم، حافظ على الصفحة الحالية والموضع
            const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
            displayMoviesWithoutScroll(appState.currentCategory, currentPage);

            // استعادة موضع التمرير بعد تحديث الواجهة
            setTimeout(() => {
                window.scrollTo(0, currentScrollPosition);
            }, 50);
        }

        // إغلاق المودال
        modal.classList.remove('show');

        showToast(`تم تحديث "${name}" بنجاح`, 'success');
    } else {
        showToast('لم يتم العثور على الفيلم', 'error');
    }
}

// Setup manage movies tab
function setupManageMoviesTab() {
    // Update category select options
    updateCategorySelectOptions();

    // Get filter elements
    const filterCategory = document.getElementById('filter-category');
    const filterSite = document.getElementById('filter-site');

    // Initially populate the site filter based on the default category
    populateSiteFilter(filterCategory.value);

    // Update site filter when category changes
    filterCategory.addEventListener('change', () => {
        populateSiteFilter(filterCategory.value);
        displayManagedMovies();
    });

    // Update display when site filter changes
    filterSite.addEventListener('change', () => {
        displayManagedMovies();

        // Show/hide site actions based on whether a site is selected
        const siteActions = document.getElementById('site-actions');
        if (filterSite.value) {
            siteActions.classList.remove('hidden');
        } else {
            siteActions.classList.add('hidden');
        }
    });

    // Site action buttons
    document.getElementById('move-site-movies').onclick = moveSiteMovies;
    document.getElementById('delete-site-movies').onclick = deleteSiteMovies;
    document.getElementById('hide-site-movies').onclick = hideSiteMovies;

    // Display the movies
    displayManagedMovies();
}

// Populate site filter dropdown based on selected category
function populateSiteFilter(categoryId) {
    const filterSite = document.getElementById('filter-site');
    const currentValue = filterSite.value;

    // Clear options
    filterSite.innerHTML = '<option value="">جميع المواقع</option>';

    // Get all unique sites from the movies in the selected category
    const sites = new Set();

    let items = [];
    if (categoryId === 'all') {
        items = [...appState.movies, ...appState.series];
    } else if (categoryId === 'series') {
        items = appState.series;
    } else {
        items = appState.movies.filter(movie => movie.category === categoryId);
    }

    items.forEach(item => {
        if (item.href) {
            try {
                const site = getSiteFromUrl(item.href);
                if (site) sites.add(site);
            } catch (e) {
                // Ignore invalid URLs
            }
        }
    });

    // Add options with movie count (sorted alphabetically)
    const sitesArray = Array.from(sites).sort();
    sitesArray.forEach(site => {
        // Count movies for this site
        let movieCount = 0;
        items.forEach(item => {
            if (item.href) {
                try {
                    const itemSite = getSiteFromUrl(item.href);
                    if (itemSite === site) {
                        movieCount++;
                    }
                } catch (e) {
                    // Ignore invalid URLs
                }
            }
        });

        const option = document.createElement('option');
        option.value = site;
        option.textContent = `${site} (${movieCount} عنصر)`;
        filterSite.appendChild(option);
    });

    // إذا لم تكن هناك قيمة محددة مسبقاً، اختر الموقع الأول تلقائياً
    if (currentValue && Array.from(filterSite.options).some(opt => opt.value === currentValue)) {
        filterSite.value = currentValue;
    } else if (sitesArray.length > 0) {
        // اختيار الموقع الأول تلقائياً
        filterSite.value = sitesArray[0];
        // تحديث العرض بناءً على الفلتر الجديد
        setTimeout(() => displayManagedMovies(), 100);
    }
}

// Display movies based on filters
function displayManagedMovies() {
    const filterCategory = document.getElementById('filter-category');
    const filterSite = document.getElementById('filter-site');
    const container = document.getElementById('movies-management-list');

    const categoryId = filterCategory.value;
    const site = filterSite.value;

    // Clear container
    container.innerHTML = '';

    // Get filtered items
    let items = [];
    let folders = [];

    if (categoryId === 'all') {
        items = [...appState.movies, ...appState.series];
    } else if (categoryId === 'series') {
        items = appState.series;
    } else if (categoryId === 'movie-sites') {
        // إضافة المجلدات والمواقع لقسم مواقع الأفلام
        const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
        if (movieSitesCat) {
            folders = movieSitesCat.folders || [];
            items = movieSitesCat.shortcuts || [];
        }
    } else {
        items = appState.movies.filter(movie => movie.category === categoryId);
    }

    // Apply site filter
    if (site) {
        items = items.filter(item => getSiteFromUrl(item.href || item.url) === site);
    }

    // عرض المجلدات أولاً إذا كان القسم هو مواقع الأفلام
    if (categoryId === 'movie-sites' && folders.length > 0) {
        const foldersSection = document.createElement('div');
        foldersSection.className = 'folders-management-section';
        foldersSection.innerHTML = '<h4 style="color: #333; margin-bottom: 15px;"><i class="fas fa-folder" style="color: #ffa726; margin-left: 8px;"></i>المجلدات</h4>';

        folders.forEach(folder => {
            const folderCard = document.createElement('div');
            folderCard.className = 'manage-folder-card';
            folderCard.innerHTML = `
                <div class="manage-folder-header">
                    <div class="folder-info">
                        <i class="fas fa-folder" style="color: #ffa726; margin-left: 10px; font-size: 1.2em;"></i>
                        <span class="folder-name">${folder.name}</span>
                        <span class="folder-count">(${folder.sites ? folder.sites.length : 0} مواقع)</span>
                    </div>
                    <div class="folder-visibility">
                        <label class="visibility-toggle">
                            <input type="checkbox" ${folder.hidden ? '' : 'checked'} onchange="toggleFolderVisibility('${folder.id}', this.checked)">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">${folder.hidden ? 'مخفي' : 'ظاهر'}</span>
                        </label>
                    </div>
                </div>
                <div class="manage-folder-actions">
                    <button class="manage-folder-edit" onclick="renameFolderFromManagement('${folder.id}')">
                        <i class="fas fa-edit"></i> إعادة تسمية
                    </button>
                    <button class="manage-folder-delete" onclick="deleteFolderFromManagement('${folder.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="manage-folder-view" onclick="showFolderContents('${folder.id}')">
                        <i class="fas fa-eye"></i> عرض المحتوى
                    </button>
                </div>
            `;
            foldersSection.appendChild(folderCard);
        });

        container.appendChild(foldersSection);
    }

    // Display a message if no movies found
    if (items.length === 0 && folders.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد عناصر تطابق المعايير المحددة</div>';
        return;
    }

    // إضافة قسم المواقع إذا كان هناك مواقع
    if (items.length > 0) {
        const sitesSection = document.createElement('div');
        sitesSection.className = 'sites-management-section';
        if (categoryId === 'movie-sites') {
            sitesSection.innerHTML = '<h4 style="color: #333; margin: 20px 0 15px 0;"><i class="fas fa-external-link-alt" style="color: #b97cff; margin-left: 8px;"></i>المواقع في القائمة الرئيسية</h4>';
        }
        container.appendChild(sitesSection);
    }

    // Display the movies
    items.forEach(item => {
        const movieCard = document.createElement('div');
        movieCard.className = 'manage-movie-card';

        const imgFilename = getImageFilenameFromUrl(item.img);
        const imgSrc = appState.cachedImages[imgFilename] || item.img;

        movieCard.innerHTML = `
            <img src="${imgSrc}" alt="${item.name}" class="manage-movie-image" onerror="this.src='data:image/png;base64,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'">
            <div class="manage-movie-details">
                <h4>${item.name}</h4>
                <p>القسم: ${getCategoryName(item.category)}</p>
                <p>الموقع: ${getSiteFromUrl(item.href) || 'غير محدد'}</p>
            </div>
            <div class="manage-movie-actions">
                <button class="manage-movie-play" data-href="${item.href}" title="تشغيل الفيلم">
                    <i class="fas fa-play"></i> تشغيل
                </button>
                <button class="manage-movie-edit" data-id="${item.id}">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="manage-movie-delete" data-id="${item.id}">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        `;

        // إضافة البطاقة إلى القسم المناسب
        if (categoryId === 'movie-sites') {
            // تخصيص عرض المواقع
            if (item.url || item.href) {
                movieCard.innerHTML = `
                    <div class="manage-site-icon">
                        <i class="fas fa-external-link-alt" style="font-size: 2em; color: #b97cff;"></i>
                    </div>
                    <div class="manage-movie-details">
                        <h4>${item.name || item.title || item.url}</h4>
                        <p>الرابط: ${item.url || item.href}</p>
                        <p>الموقع: ${getSiteFromUrl(item.url || item.href) || 'غير محدد'}</p>
                    </div>
                    <div class="manage-movie-actions">
                        <button class="manage-site-edit" data-index="${items.indexOf(item)}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="manage-site-delete" data-index="${items.indexOf(item)}">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                        <button class="manage-site-visit" data-url="${item.url || item.href}">
                            <i class="fas fa-external-link-alt"></i> زيارة
                        </button>
                    </div>
                `;
            }

            const sitesSection = container.querySelector('.sites-management-section');
            if (sitesSection) {
                sitesSection.appendChild(movieCard);
            } else {
                container.appendChild(movieCard);
            }
        } else {
            container.appendChild(movieCard);
        }
    });

    // Add event listeners to the play, edit and delete buttons
    document.querySelectorAll('.manage-movie-play').forEach(button => {
        button.addEventListener('click', () => {
            const href = button.dataset.href;
            if (href) {
                window.open(href, '_blank');
            }
        });
    });

    document.querySelectorAll('.manage-movie-edit').forEach(button => {
        button.addEventListener('click', () => {
            const itemId = button.dataset.id;
            const item = findMovieById(itemId);
            if (item) {
                openEditModal(item);
            }
        });
    });

    document.querySelectorAll('.manage-movie-delete').forEach(button => {
        button.addEventListener('click', () => {
            const itemId = button.dataset.id;
            const item = findMovieById(itemId);

            if (item) {
                // Show confirmation modal
                const modal = document.getElementById('confirm-modal');
                const messageElement = document.getElementById('confirm-message');

                messageElement.textContent = `هل أنت متأكد من رغبتك في حذف "${item.name}"؟`;

                // Show the modal
                modal.classList.add('show');

                // Yes button
                document.getElementById('confirm-yes').onclick = () => {
                    deleteMovie(itemId);
                    displayManagedMovies();
                    modal.classList.remove('show');
                };

                // No button
                document.getElementById('confirm-no').onclick = () => {
                    modal.classList.remove('show');
                };
            }
        });
    });

    // Add event listeners for site management buttons
    document.querySelectorAll('.manage-site-edit').forEach(button => {
        button.addEventListener('click', () => {
            const siteIndex = parseInt(button.dataset.index);
            editSiteFromManagement(siteIndex);
        });
    });

    document.querySelectorAll('.manage-site-delete').forEach(button => {
        button.addEventListener('click', () => {
            const siteIndex = parseInt(button.dataset.index);
            if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
                deleteSiteFromManagement(siteIndex);
            }
        });
    });

    document.querySelectorAll('.manage-site-visit').forEach(button => {
        button.addEventListener('click', () => {
            const url = button.dataset.url;
            if (url) {
                window.open(url, '_blank');
            }
        });
    });
}

// Move site movies to another category
function moveSiteMovies() {
    const filterCategory = document.getElementById('filter-category');
    const filterSite = document.getElementById('filter-site');

    const categoryId = filterCategory.value;
    const site = filterSite.value;

    if (!site) {
        showToast('يرجى اختيار موقع', 'warning');
        return;
    }

    // Create a modal to select target category
    const modal = document.getElementById('confirm-modal');
    const messageElement = document.getElementById('confirm-message');

    // Create category select
    let categoryOptions = '';
    appState.categories.main.forEach(cat => {
        if (cat.id !== 'all') {
            categoryOptions += `<option value="${cat.id}">${cat.name}</option>`;
        }
    });

    if (appState.showSpecialSections) {
        appState.categories.special.forEach(cat => {
            categoryOptions += `<option value="${cat.id}">${cat.name}</option>`;
        });
    }

    messageElement.innerHTML = `
        <p>نقل الأفلام من موقع "${site}" إلى:</p>
        <select id="move-site-target-category" class="form-control">
            ${categoryOptions}
        </select>
    `;

    // Show the modal
    modal.classList.add('show');

    // Yes button
    document.getElementById('confirm-yes').onclick = () => {
        const targetCategory = document.getElementById('move-site-target-category').value;
        let movedCount = 0;

        // Move the movies
        appState.movies.forEach(movie => {
            if (getSiteFromUrl(movie.href) === site) {
                movie.category = targetCategory;
                movedCount++;
            }
        });

        saveAppData();
        updateCategoriesCounts();
        renderCategories();
        displayManagedMovies();

        modal.classList.remove('show');
        showToast(`تم نقل ${movedCount} فيلم من موقع "${site}" إلى قسم "${getCategoryName(targetCategory)}" بنجاح`, 'success');
    };

    // No button
    document.getElementById('confirm-no').onclick = () => {
        modal.classList.remove('show');
    };
}

// Delete site movies
function deleteSiteMovies() {
    const filterSite = document.getElementById('filter-site');
    const site = filterSite.value;

    if (!site) {
        showToast('يرجى اختيار موقع', 'warning');
        return;
    }

    // Show confirmation modal
    const modal = document.getElementById('confirm-modal');
    const messageElement = document.getElementById('confirm-message');

    messageElement.textContent = `هل أنت متأكد من رغبتك في حذف جميع الأفلام من موقع "${site}"؟`;

    // Show the modal
    modal.classList.add('show');

    // Yes button
    document.getElementById('confirm-yes').onclick = () => {
        const originalMoviesCount = appState.movies.length;
        const originalSeriesCount = appState.series.length;

        // Filter out the movies from the specified site
        appState.movies = appState.movies.filter(movie => getSiteFromUrl(movie.href) !== site);
        appState.series = appState.series.filter(series => getSiteFromUrl(series.href) !== site);

        // Calculate deleted count
        const deletedMoviesCount = originalMoviesCount - appState.movies.length;
        const deletedSeriesCount = originalSeriesCount - appState.series.length;
        const totalDeleted = deletedMoviesCount + deletedSeriesCount;

        saveAppData();
        updateCategoriesCounts();
        renderCategories();
        displayManagedMovies();

        modal.classList.remove('show');
        showToast(`تم حذف ${totalDeleted} عنصر من موقع "${site}" بنجاح`, 'success');
    };

    // No button
    document.getElementById('confirm-no').onclick = () => {
        modal.classList.remove('show');
    };
}

// Hide site movies
function hideSiteMovies() {
    const filterSite = document.getElementById('filter-site');
    const site = filterSite.value;

    if (!site) {
        showToast('يرجى اختيار موقع', 'warning');
        return;
    }

    // Show confirmation modal
    const modal = document.getElementById('confirm-modal');
    const messageElement = document.getElementById('confirm-message');

    messageElement.textContent = `هل أنت متأكد من رغبتك في إخفاء جميع الأفلام من موقع "${site}"؟`;

    // Show the modal
    modal.classList.add('show');

    // Yes button
    document.getElementById('confirm-yes').onclick = () => {
        let hiddenCount = 0;

        // Hide the movies
        appState.movies.forEach(movie => {
            if (getSiteFromUrl(movie.href) === site) {
                movie.hidden = true;
                hiddenCount++;
            }
        });

        appState.series.forEach(series => {
            if (getSiteFromUrl(series.href) === site) {
                series.hidden = true;
                hiddenCount++;
            }
        });

        saveAppData();
        updateCategoriesCounts();
        renderCategories();
        displayManagedMovies();

        modal.classList.remove('show');
        showToast(`تم إخفاء ${hiddenCount} عنصر من موقع "${site}" بنجاح`, 'success');
    };

    // No button
    document.getElementById('confirm-no').onclick = () => {
        modal.classList.remove('show');
    };
}

// Setup manage sites tab
function setupManageSitesTab() {
    // تحديث قائمة المواقع عند فتح التبويب
    populateSitesSelect();

    // إعداد قائمة المواقع المنسدلة
    const sitesSelect = document.getElementById('sites-select');
    if (sitesSelect) {
        sitesSelect.addEventListener('change', handleSiteSelection);
    }

    // إعداد زر تحديث القائمة
    const refreshBtn = document.getElementById('refresh-sites-select');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', populateSitesSelect);
    }

    // إعداد أزرار العمليات
    setupSiteActionButtons();

    // إعداد مودال العمليات الجماعية
    setupSiteBulkActionModal();
}

// تعبئة قائمة المواقع المنسدلة
function populateSitesSelect() {
    const sitesSelect = document.getElementById('sites-select');
    if (!sitesSelect) return;

    // الحصول على جميع المواقع مع عدد الأفلام لكل موقع
    const sitesData = getAllSitesWithCounts();

    // حفظ القيمة المحددة حالياً
    const currentValue = sitesSelect.value;

    // تفريغ القائمة
    sitesSelect.innerHTML = '<option value="">-- اختر موقعاً --</option>';

    if (sitesData.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'لا توجد مواقع متاحة';
        option.disabled = true;
        sitesSelect.appendChild(option);
        return;
    }

    // إضافة المواقع للقائمة
    sitesData.forEach(siteData => {
        const option = document.createElement('option');
        option.value = siteData.site;
        option.textContent = `${siteData.site} (${siteData.totalCount} عنصر)`;
        sitesSelect.appendChild(option);
    });

    // استعادة القيمة المحددة إذا كانت موجودة
    if (currentValue && sitesData.some(s => s.site === currentValue)) {
        sitesSelect.value = currentValue;
        handleSiteSelection(); // تحديث المعلومات
    } else {
        // إخفاء معلومات الموقع
        const siteInfo = document.getElementById('selected-site-info');
        if (siteInfo) {
            siteInfo.classList.add('hidden');
        }
    }
}

// معالجة اختيار موقع من القائمة
function handleSiteSelection() {
    const sitesSelect = document.getElementById('sites-select');
    const siteInfo = document.getElementById('selected-site-info');

    if (!sitesSelect || !siteInfo) return;

    const selectedSite = sitesSelect.value;

    if (!selectedSite) {
        siteInfo.classList.add('hidden');
        // إخفاء معلومات القسم أيضاً
        const siteLocationElement = document.getElementById('selected-site-location');
        if (siteLocationElement) {
            siteLocationElement.classList.add('hidden');
        }
        return;
    }

    // الحصول على بيانات الموقع المحدد
    const sitesData = getAllSitesWithCounts();
    const siteData = sitesData.find(s => s.site === selectedSite);

    if (!siteData) {
        siteInfo.classList.add('hidden');
        return;
    }

    // الحصول على معلومات موقع الموقع في قسم مواقع الأفلام
    const siteLocation = getSiteLocation(selectedSite);

    // تحديث معلومات الموقع
    document.getElementById('selected-site-name').textContent = siteData.site;
    document.getElementById('selected-site-url').textContent = siteData.site;
    document.getElementById('selected-site-movies').textContent = siteData.moviesCount;
    document.getElementById('selected-site-series').textContent = siteData.seriesCount;
    document.getElementById('selected-site-total').textContent = siteData.totalCount;

    // الحصول على بيان شامل بجميع الأقسام التي تحتوي على أفلام الموقع
    const comprehensiveSiteReport = getComprehensiveSiteReport(selectedSite);

    // تحديث معلومات القسم
    const siteLocationElement = document.getElementById('selected-site-location');
    if (siteLocationElement) {
        if (comprehensiveSiteReport && comprehensiveSiteReport.totalCategories > 0) {
            let locationHTML = `
                <p><strong><i class="fas fa-chart-bar"></i> بيان شامل - الموقع موجود في ${comprehensiveSiteReport.totalCategories} قسم:</strong></p>
                <div class="comprehensive-report">
            `;

            // إضافة قسم مواقع الأفلام إذا كان موجوداً
            if (comprehensiveSiteReport.movieSitesSection) {
                const section = comprehensiveSiteReport.movieSitesSection;
                locationHTML += `
                    <div class="report-section movie-sites-section">
                        <h4><i class="fas fa-external-link-alt"></i> قسم مواقع الأفلام</h4>
                        <div class="section-content">
                            <p><strong>الموقع:</strong> ${section.location}</p>
                            <p class="section-note">هذا الموقع مضاف كاختصار في قسم مواقع الأفلام</p>
                        </div>
                    </div>
                `;
            }

            // إضافة الأقسام التي تحتوي على أفلام/مسلسلات
            if (comprehensiveSiteReport.contentCategories.length > 0) {
                locationHTML += `
                    <div class="report-section content-categories-section">
                        <h4><i class="fas fa-film"></i> الأقسام التي تحتوي على محتوى من الموقع (${comprehensiveSiteReport.contentCategories.length} قسم)</h4>
                        <div class="categories-list">
                `;

                comprehensiveSiteReport.contentCategories.forEach(cat => {
                    const totalCount = cat.moviesCount + cat.seriesCount;
                    let typeIcon = '';
                    let typeText = '';

                    switch(cat.type) {
                        case 'main':
                            typeIcon = 'fas fa-folder';
                            typeText = 'قسم رئيسي';
                            break;
                        case 'special':
                            typeIcon = 'fas fa-star';
                            typeText = 'قسم خاص';
                            break;
                        case 'sub':
                            typeIcon = 'fas fa-folder-open';
                            typeText = 'قسم فرعي';
                            break;
                    }

                    locationHTML += `
                        <div class="category-item">
                            <div class="category-info">
                                <p><i class="${typeIcon}"></i> <strong>${cat.categoryName}</strong> (${typeText})</p>
                                <p class="category-stats">
                                    <span>أفلام: ${cat.moviesCount}</span>
                                    ${cat.seriesCount > 0 ? `<span>مسلسلات: ${cat.seriesCount}</span>` : ''}
                                    <span class="total">المجموع: ${totalCount}</span>
                                </p>
                            </div>
                            <div class="category-actions">
                                <button class="category-action-btn move" onclick="showCategorySiteBulkActionModal('${selectedSite}', '${cat.categoryId}', 'move')" title="نقل أفلام الموقع من هذا القسم">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                                <button class="category-action-btn delete" onclick="showCategorySiteBulkActionModal('${selectedSite}', '${cat.categoryId}', 'delete')" title="حذف أفلام الموقع من هذا القسم">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button class="category-action-btn hide" onclick="showCategorySiteBulkActionModal('${selectedSite}', '${cat.categoryId}', 'hide')" title="إخفاء أفلام الموقع من هذا القسم">
                                    <i class="fas fa-eye-slash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });

                locationHTML += `
                        </div>
                    </div>
                `;
            }

            // إضافة ملخص إحصائي
            locationHTML += `
                <div class="report-section summary-section">
                    <h4><i class="fas fa-chart-pie"></i> ملخص إحصائي</h4>
                    <div class="summary-stats">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الأقسام:</span>
                            <span class="summary-value">${comprehensiveSiteReport.totalCategories}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">إجمالي الأفلام:</span>
                            <span class="summary-value">${comprehensiveSiteReport.totalMovies}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">إجمالي المسلسلات:</span>
                            <span class="summary-value">${comprehensiveSiteReport.totalSeries}</span>
                        </div>
                        <div class="summary-item total">
                            <span class="summary-label">المجموع الكلي:</span>
                            <span class="summary-value">${comprehensiveSiteReport.grandTotal}</span>
                        </div>
                    </div>
                </div>
            `;

            locationHTML += '</div>';

            siteLocationElement.innerHTML = locationHTML;
            siteLocationElement.classList.remove('hidden');
        } else {
            siteLocationElement.innerHTML = '<p><i class="fas fa-exclamation-triangle"></i> الموقع غير موجود في أي قسم</p>';
            siteLocationElement.classList.remove('hidden');
        }
    }

    // إظهار معلومات الموقع
    siteInfo.classList.remove('hidden');
}

// إعداد أزرار العمليات
function setupSiteActionButtons() {
    const moveBtn = document.getElementById('move-site-btn');
    const deleteBtn = document.getElementById('delete-site-btn');
    const hideBtn = document.getElementById('hide-site-btn');

    if (moveBtn) {
        moveBtn.addEventListener('click', () => {
            const selectedSite = document.getElementById('sites-select').value;
            if (selectedSite) {
                showSiteBulkActionModal(selectedSite, 'move');
            }
        });
    }

    if (deleteBtn) {
        deleteBtn.addEventListener('click', () => {
            const selectedSite = document.getElementById('sites-select').value;
            if (selectedSite) {
                showSiteBulkActionModal(selectedSite, 'delete');
            }
        });
    }

    if (hideBtn) {
        hideBtn.addEventListener('click', () => {
            const selectedSite = document.getElementById('sites-select').value;
            if (selectedSite) {
                showSiteBulkActionModal(selectedSite, 'hide');
            }
        });
    }
}

// الحصول على جميع المواقع مع عدد الأفلام
function getAllSitesWithCounts() {
    const sitesMap = new Map();

    // معالجة الأفلام
    appState.movies.forEach(movie => {
        if (movie.href && !movie.hidden) {
            const site = getSiteFromUrl(movie.href);
            if (site) {
                if (!sitesMap.has(site)) {
                    sitesMap.set(site, { site, moviesCount: 0, seriesCount: 0 });
                }
                sitesMap.get(site).moviesCount++;
            }
        }
    });

    // معالجة المسلسلات
    appState.series.forEach(series => {
        if (series.href && !series.hidden) {
            const site = getSiteFromUrl(series.href);
            if (site) {
                if (!sitesMap.has(site)) {
                    sitesMap.set(site, { site, moviesCount: 0, seriesCount: 0 });
                }
                sitesMap.get(site).seriesCount++;
            }
        }
    });

    // تحويل إلى مصفوفة وإضافة المجموع
    const sitesArray = Array.from(sitesMap.values()).map(siteData => ({
        ...siteData,
        totalCount: siteData.moviesCount + siteData.seriesCount
    }));

    // ترتيب حسب العدد الإجمالي (تنازلي)
    return sitesArray.sort((a, b) => b.totalCount - a.totalCount);
}

// تحديد القسم أو المجلد الذي يحتوي على موقع معين
function getSiteLocation(siteName) {
    if (!siteName) return null;

    // البحث في قسم مواقع الأفلام أولاً
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (movieSitesCat) {
        // البحث في القائمة الرئيسية
        if (movieSitesCat.shortcuts && movieSitesCat.shortcuts.length > 0) {
            const siteInMain = movieSitesCat.shortcuts.find(site => {
                const siteUrl = site.url || site.href;
                return siteUrl && getSiteFromUrl(siteUrl) === siteName;
            });

            if (siteInMain) {
                return {
                    type: 'main',
                    location: 'القائمة الرئيسية',
                    categoryName: 'مواقع الأفلام'
                };
            }
        }

        // البحث في المجلدات
        if (movieSitesCat.folders && movieSitesCat.folders.length > 0) {
            for (const folder of movieSitesCat.folders) {
                if (folder.sites && folder.sites.length > 0) {
                    const siteInFolder = folder.sites.find(site => {
                        const siteUrl = site.url || site.href;
                        return siteUrl && getSiteFromUrl(siteUrl) === siteName;
                    });

                    if (siteInFolder) {
                        return {
                            type: 'folder',
                            location: folder.name,
                            categoryName: 'مواقع الأفلام',
                            folderId: folder.id
                        };
                    }
                }
            }
        }
    }

    // البحث في الأقسام الأخرى عن الأفلام والمسلسلات التي تحتوي على هذا الموقع
    const categoriesWithSite = [];

    // البحث في الأقسام الرئيسية
    for (const category of appState.categories.main) {
        if (category.id === 'movie-sites') continue; // تم البحث فيه بالفعل

        const hasMoviesFromSite = appState.movies.some(movie =>
            movie.category === category.id &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        );

        const hasSeriesFromSite = appState.series.some(series =>
            series.category === category.id &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        );

        if (hasMoviesFromSite || hasSeriesFromSite) {
            categoriesWithSite.push({
                type: 'main',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: appState.movies.filter(movie =>
                    movie.category === category.id &&
                    !movie.hidden &&
                    movie.href &&
                    getSiteFromUrl(movie.href) === siteName
                ).length,
                seriesCount: appState.series.filter(series =>
                    series.category === category.id &&
                    !series.hidden &&
                    series.href &&
                    getSiteFromUrl(series.href) === siteName
                ).length
            });
        }
    }

    // البحث في الأقسام الخاصة
    for (const category of appState.categories.special) {
        const hasMoviesFromSite = appState.movies.some(movie =>
            movie.category === category.id &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        );

        if (hasMoviesFromSite) {
            categoriesWithSite.push({
                type: 'special',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: appState.movies.filter(movie =>
                    movie.category === category.id &&
                    !movie.hidden &&
                    movie.href &&
                    getSiteFromUrl(movie.href) === siteName
                ).length,
                seriesCount: 0
            });
        }
    }

    // البحث في الأقسام الفرعية
    for (const category of [...appState.categories.sub, ...appState.categories.specialSub]) {
        const hasMoviesFromSite = appState.movies.some(movie =>
            movie.subCategories &&
            movie.subCategories.includes(category.id) &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        );

        const hasSeriesFromSite = appState.series.some(series =>
            series.subCategories &&
            series.subCategories.includes(category.id) &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        );

        if (hasMoviesFromSite || hasSeriesFromSite) {
            categoriesWithSite.push({
                type: 'sub',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: appState.movies.filter(movie =>
                    movie.subCategories &&
                    movie.subCategories.includes(category.id) &&
                    !movie.hidden &&
                    movie.href &&
                    getSiteFromUrl(movie.href) === siteName
                ).length,
                seriesCount: appState.series.filter(series =>
                    series.subCategories &&
                    series.subCategories.includes(category.id) &&
                    !series.hidden &&
                    series.href &&
                    getSiteFromUrl(series.href) === siteName
                ).length
            });
        }
    }

    // إرجاع النتائج
    if (categoriesWithSite.length > 0) {
        return {
            type: 'categories',
            categories: categoriesWithSite,
            totalCategories: categoriesWithSite.length
        };
    }

    return null;
}

// الحصول على بيان شامل بجميع الأقسام التي تحتوي على أفلام الموقع
function getComprehensiveSiteReport(siteName) {
    if (!siteName) return null;

    const report = {
        movieSitesSection: null,
        contentCategories: [],
        totalCategories: 0,
        totalMovies: 0,
        totalSeries: 0,
        grandTotal: 0
    };

    // البحث في قسم مواقع الأفلام
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (movieSitesCat) {
        // البحث في القائمة الرئيسية
        if (movieSitesCat.shortcuts && movieSitesCat.shortcuts.length > 0) {
            const siteInMain = movieSitesCat.shortcuts.find(site => {
                const siteUrl = site.url || site.href;
                return siteUrl && getSiteFromUrl(siteUrl) === siteName;
            });

            if (siteInMain) {
                report.movieSitesSection = {
                    type: 'main',
                    location: 'القائمة الرئيسية',
                    categoryName: 'مواقع الأفلام'
                };
                report.totalCategories++;
            }
        }

        // البحث في المجلدات
        if (!report.movieSitesSection && movieSitesCat.folders && movieSitesCat.folders.length > 0) {
            for (const folder of movieSitesCat.folders) {
                if (folder.sites && folder.sites.length > 0) {
                    const siteInFolder = folder.sites.find(site => {
                        const siteUrl = site.url || site.href;
                        return siteUrl && getSiteFromUrl(siteUrl) === siteName;
                    });

                    if (siteInFolder) {
                        report.movieSitesSection = {
                            type: 'folder',
                            location: folder.name,
                            categoryName: 'مواقع الأفلام',
                            folderId: folder.id
                        };
                        report.totalCategories++;
                        break;
                    }
                }
            }
        }
    }

    // البحث في الأقسام الرئيسية
    for (const category of appState.categories.main) {
        if (category.id === 'movie-sites') continue; // تم البحث فيه بالفعل

        const moviesCount = appState.movies.filter(movie =>
            movie.category === category.id &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        ).length;

        const seriesCount = appState.series.filter(series =>
            series.category === category.id &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        ).length;

        if (moviesCount > 0 || seriesCount > 0) {
            report.contentCategories.push({
                type: 'main',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: moviesCount,
                seriesCount: seriesCount
            });
            report.totalCategories++;
            report.totalMovies += moviesCount;
            report.totalSeries += seriesCount;
        }
    }

    // البحث في الأقسام الخاصة
    for (const category of appState.categories.special) {
        const moviesCount = appState.movies.filter(movie =>
            movie.category === category.id &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        ).length;

        if (moviesCount > 0) {
            report.contentCategories.push({
                type: 'special',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: moviesCount,
                seriesCount: 0
            });
            report.totalCategories++;
            report.totalMovies += moviesCount;
        }
    }

    // البحث في الأقسام الفرعية
    for (const category of [...appState.categories.sub, ...appState.categories.specialSub]) {
        const moviesCount = appState.movies.filter(movie =>
            movie.subCategories &&
            movie.subCategories.includes(category.id) &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        ).length;

        const seriesCount = appState.series.filter(series =>
            series.subCategories &&
            series.subCategories.includes(category.id) &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        ).length;

        if (moviesCount > 0 || seriesCount > 0) {
            report.contentCategories.push({
                type: 'sub',
                categoryId: category.id,
                categoryName: category.name,
                moviesCount: moviesCount,
                seriesCount: seriesCount
            });
            report.totalCategories++;
            report.totalMovies += moviesCount;
            report.totalSeries += seriesCount;
        }
    }

    // حساب المجموع الكلي
    report.grandTotal = report.totalMovies + report.totalSeries;

    // ترتيب الأقسام حسب العدد الإجمالي (تنازلي)
    report.contentCategories.sort((a, b) => {
        const totalA = a.moviesCount + a.seriesCount;
        const totalB = b.moviesCount + b.seriesCount;
        return totalB - totalA;
    });

    return report.totalCategories > 0 ? report : null;
}

// إظهار مودال العمليات الجماعية للموقع في قسم محدد
function showCategorySiteBulkActionModal(siteName, categoryId, action) {
    if (!siteName || !categoryId || !action) return;

    const modal = document.getElementById('site-bulk-action-modal');
    const titleElement = document.getElementById('site-action-title');
    const messageElement = document.getElementById('site-action-message');
    const optionsElement = document.getElementById('site-action-options');
    const confirmBtn = document.getElementById('site-action-confirm');

    if (!modal || !titleElement || !messageElement || !confirmBtn) return;

    // الحصول على معلومات القسم
    const categoryName = getCategoryName(categoryId);

    // حساب عدد العناصر في هذا القسم من هذا الموقع
    let moviesCount = 0;
    let seriesCount = 0;

    // تحديد نوع القسم للبحث الصحيح
    const isMainCategory = appState.categories.main.some(cat => cat.id === categoryId);
    const isSpecialCategory = appState.categories.special.some(cat => cat.id === categoryId);
    const isSubCategory = [...appState.categories.sub, ...appState.categories.specialSub].some(cat => cat.id === categoryId);

    if (isMainCategory || isSpecialCategory) {
        // البحث في الأقسام الرئيسية والخاصة
        moviesCount = appState.movies.filter(movie =>
            movie.category === categoryId &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        ).length;

        seriesCount = appState.series.filter(series =>
            series.category === categoryId &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        ).length;
    } else if (isSubCategory) {
        // البحث في الأقسام الفرعية
        moviesCount = appState.movies.filter(movie =>
            movie.subCategories &&
            movie.subCategories.includes(categoryId) &&
            !movie.hidden &&
            movie.href &&
            getSiteFromUrl(movie.href) === siteName
        ).length;

        seriesCount = appState.series.filter(series =>
            series.subCategories &&
            series.subCategories.includes(categoryId) &&
            !series.hidden &&
            series.href &&
            getSiteFromUrl(series.href) === siteName
        ).length;
    }

    const totalCount = moviesCount + seriesCount;

    if (totalCount === 0) {
        showToast('لا توجد عناصر من هذا الموقع في هذا القسم', 'info');
        return;
    }

    // تحديد النص والخيارات حسب نوع العملية
    let title, message, showOptions = false;

    switch (action) {
        case 'move':
            title = 'نقل أفلام الموقع من القسم';
            message = `هل تريد نقل جميع العناصر (${totalCount} عنصر) من موقع "${siteName}" الموجودة في قسم "${categoryName}" إلى قسم آخر؟`;
            showOptions = true;
            break;
        case 'delete':
            title = 'حذف أفلام الموقع من القسم';
            message = `هل تريد حذف جميع العناصر (${totalCount} عنصر) من موقع "${siteName}" الموجودة في قسم "${categoryName}" نهائياً؟\n\nتحذير: هذه العملية لا يمكن التراجع عنها!`;
            break;
        case 'hide':
            title = 'إخفاء أفلام الموقع من القسم';
            message = `هل تريد إخفاء جميع العناصر (${totalCount} عنصر) من موقع "${siteName}" الموجودة في قسم "${categoryName}"؟\n\nيمكنك إظهارها لاحقاً من إدارة الأفلام.`;
            break;
    }

    // تحديث محتوى المودال
    titleElement.textContent = title;
    messageElement.textContent = message;

    // إظهار/إخفاء خيارات النقل
    if (showOptions) {
        populateCategorySelect('site-target-category-select');
        optionsElement.classList.remove('hidden');
    } else {
        optionsElement.classList.add('hidden');
    }

    // إعداد زر التأكيد
    confirmBtn.onclick = () => {
        executeCategorySiteBulkAction(siteName, categoryId, action);
        modal.classList.remove('show');
    };

    // إظهار المودال
    modal.classList.add('show');
}

// تنفيذ العمليات الجماعية للموقع في قسم محدد
function executeCategorySiteBulkAction(siteName, categoryId, action) {
    if (!siteName || !categoryId || !action) return;

    const categoryName = getCategoryName(categoryId);
    let affectedCount = 0;

    // تحديد نوع القسم للبحث الصحيح
    const isMainCategory = appState.categories.main.some(cat => cat.id === categoryId);
    const isSpecialCategory = appState.categories.special.some(cat => cat.id === categoryId);
    const isSubCategory = [...appState.categories.sub, ...appState.categories.specialSub].some(cat => cat.id === categoryId);

    switch (action) {
        case 'move':
            const targetCategory = document.getElementById('site-target-category-select').value;
            if (!targetCategory) {
                showToast('يرجى اختيار قسم مستهدف', 'warning');
                return;
            }

            if (isMainCategory || isSpecialCategory) {
                // نقل من الأقسام الرئيسية والخاصة
                appState.movies.forEach(movie => {
                    if (movie.category === categoryId &&
                        !movie.hidden &&
                        movie.href &&
                        getSiteFromUrl(movie.href) === siteName) {
                        movie.category = targetCategory;
                        affectedCount++;
                    }
                });

                appState.series.forEach(series => {
                    if (series.category === categoryId &&
                        !series.hidden &&
                        series.href &&
                        getSiteFromUrl(series.href) === siteName) {
                        series.category = targetCategory;
                        affectedCount++;
                    }
                });
            } else if (isSubCategory) {
                // نقل من الأقسام الفرعية (إزالة من القسم الفرعي وإضافة للقسم الجديد)
                appState.movies.forEach(movie => {
                    if (movie.subCategories &&
                        movie.subCategories.includes(categoryId) &&
                        !movie.hidden &&
                        movie.href &&
                        getSiteFromUrl(movie.href) === siteName) {
                        // إزالة من القسم الفرعي الحالي
                        movie.subCategories = movie.subCategories.filter(subCat => subCat !== categoryId);
                        // إضافة للقسم الجديد
                        movie.category = targetCategory;
                        affectedCount++;
                    }
                });

                appState.series.forEach(series => {
                    if (series.subCategories &&
                        series.subCategories.includes(categoryId) &&
                        !series.hidden &&
                        series.href &&
                        getSiteFromUrl(series.href) === siteName) {
                        // إزالة من القسم الفرعي الحالي
                        series.subCategories = series.subCategories.filter(subCat => subCat !== categoryId);
                        // إضافة للقسم الجديد
                        series.category = targetCategory;
                        affectedCount++;
                    }
                });
            }

            showToast(`تم نقل ${affectedCount} عنصر من موقع "${siteName}" من قسم "${categoryName}" إلى قسم "${getCategoryName(targetCategory)}" بنجاح`, 'success');
            break;

        case 'delete':
            if (isMainCategory || isSpecialCategory) {
                // حذف من الأقسام الرئيسية والخاصة
                const originalMoviesLength = appState.movies.length;
                const originalSeriesLength = appState.series.length;

                appState.movies = appState.movies.filter(movie =>
                    !(movie.category === categoryId &&
                      !movie.hidden &&
                      movie.href &&
                      getSiteFromUrl(movie.href) === siteName)
                );

                appState.series = appState.series.filter(series =>
                    !(series.category === categoryId &&
                      !series.hidden &&
                      series.href &&
                      getSiteFromUrl(series.href) === siteName)
                );

                affectedCount = (originalMoviesLength - appState.movies.length) + (originalSeriesLength - appState.series.length);
            } else if (isSubCategory) {
                // حذف من الأقسام الفرعية (إزالة من القسم الفرعي فقط)
                appState.movies.forEach(movie => {
                    if (movie.subCategories &&
                        movie.subCategories.includes(categoryId) &&
                        !movie.hidden &&
                        movie.href &&
                        getSiteFromUrl(movie.href) === siteName) {
                        movie.subCategories = movie.subCategories.filter(subCat => subCat !== categoryId);
                        affectedCount++;
                    }
                });

                appState.series.forEach(series => {
                    if (series.subCategories &&
                        series.subCategories.includes(categoryId) &&
                        !series.hidden &&
                        series.href &&
                        getSiteFromUrl(series.href) === siteName) {
                        series.subCategories = series.subCategories.filter(subCat => subCat !== categoryId);
                        affectedCount++;
                    }
                });
            }

            showToast(`تم حذف ${affectedCount} عنصر من موقع "${siteName}" من قسم "${categoryName}" بنجاح`, 'success');
            break;

        case 'hide':
            if (isMainCategory || isSpecialCategory) {
                // إخفاء من الأقسام الرئيسية والخاصة
                appState.movies.forEach(movie => {
                    if (movie.category === categoryId &&
                        !movie.hidden &&
                        movie.href &&
                        getSiteFromUrl(movie.href) === siteName) {
                        movie.hidden = true;
                        affectedCount++;
                    }
                });

                appState.series.forEach(series => {
                    if (series.category === categoryId &&
                        !series.hidden &&
                        series.href &&
                        getSiteFromUrl(series.href) === siteName) {
                        series.hidden = true;
                        affectedCount++;
                    }
                });
            } else if (isSubCategory) {
                // إخفاء من الأقسام الفرعية
                appState.movies.forEach(movie => {
                    if (movie.subCategories &&
                        movie.subCategories.includes(categoryId) &&
                        !movie.hidden &&
                        movie.href &&
                        getSiteFromUrl(movie.href) === siteName) {
                        movie.hidden = true;
                        affectedCount++;
                    }
                });

                appState.series.forEach(series => {
                    if (series.subCategories &&
                        series.subCategories.includes(categoryId) &&
                        !series.hidden &&
                        series.href &&
                        getSiteFromUrl(series.href) === siteName) {
                        series.hidden = true;
                        affectedCount++;
                    }
                });
            }

            showToast(`تم إخفاء ${affectedCount} عنصر من موقع "${siteName}" من قسم "${categoryName}" بنجاح`, 'success');
            break;
    }

    // حفظ البيانات وتحديث الواجهة
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث قائمة المواقع وإعادة عرض معلومات الموقع
    populateSitesSelect();

    // إعادة تحديد الموقع لتحديث المعلومات
    const sitesSelect = document.getElementById('sites-select');
    if (sitesSelect) {
        sitesSelect.value = siteName;
        handleSiteSelection();
    }

    // تحديث العرض الحالي إذا لزم الأمر
    if (appState.currentCategory !== 'all') {
        displayMovies(appState.currentCategory, appState.currentPage);
    }
}



// إعداد مودال العمليات الجماعية للمواقع
function setupSiteBulkActionModal() {
    const modal = document.getElementById('site-bulk-action-modal');
    if (!modal) return;

    // إعداد أزرار الإغلاق
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = document.getElementById('site-action-cancel');

    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            modal.classList.remove('show');
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            modal.classList.remove('show');
        });
    }

    // إغلاق عند النقر خارج المودال
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    });
}

// إظهار مودال العمليات الجماعية للمواقع
function showSiteBulkActionModal(site, action) {
    const modal = document.getElementById('site-bulk-action-modal');
    const titleElement = document.getElementById('site-action-title');
    const messageElement = document.getElementById('site-action-message');
    const optionsElement = document.getElementById('site-action-options');
    const confirmBtn = document.getElementById('site-action-confirm');

    if (!modal || !titleElement || !messageElement || !confirmBtn) return;

    // الحصول على عدد العناصر للموقع
    const siteData = getAllSitesWithCounts().find(s => s.site === site);
    const totalCount = siteData ? siteData.totalCount : 0;

    // تحديد النص والخيارات حسب نوع العملية
    let title, message, showOptions = false;

    switch (action) {
        case 'move':
            title = 'نقل أفلام الموقع';
            message = `هل تريد نقل جميع الأفلام والمسلسلات (${totalCount} عنصر) من موقع "${site}" إلى قسم آخر؟`;
            showOptions = true;
            break;
        case 'delete':
            title = 'حذف أفلام الموقع';
            message = `هل تريد حذف جميع الأفلام والمسلسلات (${totalCount} عنصر) من موقع "${site}" نهائياً؟\n\nتحذير: هذه العملية لا يمكن التراجع عنها!`;
            break;
        case 'hide':
            title = 'إخفاء أفلام الموقع';
            message = `هل تريد إخفاء جميع الأفلام والمسلسلات (${totalCount} عنصر) من موقع "${site}"؟\n\nيمكنك إظهارها لاحقاً من إدارة الأفلام.`;
            break;
    }

    // تحديث محتوى المودال
    titleElement.textContent = title;
    messageElement.textContent = message;

    // إظهار/إخفاء خيارات القسم المستهدف
    if (showOptions) {
        optionsElement.classList.remove('hidden');
        updateTargetCategorySelect();
    } else {
        optionsElement.classList.add('hidden');
    }

    // إعداد زر التأكيد
    confirmBtn.onclick = () => {
        executeSiteBulkAction(site, action);
        modal.classList.remove('show');
    };

    // إظهار المودال
    modal.classList.add('show');
}

// تحديث قائمة الأقسام المستهدفة
function updateTargetCategorySelect() {
    const select = document.getElementById('site-target-category-select');
    if (!select) {
        console.log('لم يتم العثور على عنصر site-target-category-select');
        return;
    }

    // تفريغ الخيارات
    select.innerHTML = '';

    // إضافة خيار افتراضي
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر قسماً --';
    select.appendChild(defaultOption);

    // إضافة الأقسام الرئيسية
    appState.categories.main.forEach(category => {
        if (category.id !== 'movie-sites' && category.id !== 'all') { // استبعاد قسم مواقع الأفلام وجميع الأفلام
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        }
    });

    // إضافة الأقسام الخاصة إذا كانت مرئية
    if (appState.showSpecialSections) {
        appState.categories.special.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            select.appendChild(option);
        });
    }

    console.log(`تم تحديث قائمة الأقسام المستهدفة بـ ${select.options.length} خيار`);
}

// تنفيذ العمليات الجماعية للمواقع
function executeSiteBulkAction(site, action) {
    let affectedCount = 0;

    switch (action) {
        case 'move':
            const targetCategory = document.getElementById('site-target-category-select').value;
            if (!targetCategory) {
                showToast('يرجى اختيار قسم مستهدف', 'warning');
                return;
            }

            // نقل الأفلام
            appState.movies.forEach(movie => {
                if (getSiteFromUrl(movie.href) === site) {
                    movie.category = targetCategory;
                    affectedCount++;
                }
            });

            // نقل المسلسلات
            appState.series.forEach(series => {
                if (getSiteFromUrl(series.href) === site) {
                    series.category = targetCategory;
                    affectedCount++;
                }
            });

            showToast(`تم نقل ${affectedCount} عنصر من موقع "${site}" إلى قسم "${getCategoryName(targetCategory)}" بنجاح`, 'success');
            break;

        case 'delete':
            const originalMoviesCount = appState.movies.length;
            const originalSeriesCount = appState.series.length;

            // حذف الأفلام
            appState.movies = appState.movies.filter(movie => getSiteFromUrl(movie.href) !== site);

            // حذف المسلسلات
            appState.series = appState.series.filter(series => getSiteFromUrl(series.href) !== site);

            affectedCount = (originalMoviesCount - appState.movies.length) + (originalSeriesCount - appState.series.length);
            showToast(`تم حذف ${affectedCount} عنصر من موقع "${site}" بنجاح`, 'success');
            break;

        case 'hide':
            // إخفاء الأفلام
            appState.movies.forEach(movie => {
                if (getSiteFromUrl(movie.href) === site) {
                    movie.hidden = true;
                    affectedCount++;
                }
            });

            // إخفاء المسلسلات
            appState.series.forEach(series => {
                if (getSiteFromUrl(series.href) === site) {
                    series.hidden = true;
                    affectedCount++;
                }
            });

            showToast(`تم إخفاء ${affectedCount} عنصر من موقع "${site}" بنجاح`, 'success');
            break;
    }

    // حفظ البيانات وتحديث الواجهة
    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث قائمة المواقع
    populateSitesSelect();

    // تحديث العرض الحالي إذا لزم الأمر
    if (appState.currentCategory !== 'all') {
        displayMovies(appState.currentCategory, appState.currentPage);
    }
}

// إدارة المجلدات من قسم إدارة الأفلام
function toggleFolderVisibility(folderId, isVisible) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder) return;

    folder.hidden = !isVisible;
    saveAppData();

    // تحديث العرض
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم ${isVisible ? 'إظهار' : 'إخفاء'} مجلد "${folder.name}" بنجاح`, 'success');
}

function renameFolderFromManagement(folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder) return;

    const newName = prompt('أدخل الاسم الجديد للمجلد:', folder.name);
    if (newName && newName.trim()) {
        renameSiteFolder(folderId, newName);
        displayManagedMovies(); // تحديث العرض
    }
}

function deleteFolderFromManagement(folderId) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder) return;

    if (confirm(`هل أنت متأكد من حذف مجلد "${folder.name}"؟\nسيتم نقل محتوياته إلى القائمة الرئيسية.`)) {
        deleteSiteFolder(folderId);
        displayManagedMovies(); // تحديث العرض
    }
}

function editSiteFromManagement(siteIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat || !movieSitesCat.shortcuts || siteIndex >= movieSitesCat.shortcuts.length) return;

    const site = movieSitesCat.shortcuts[siteIndex];
    const newName = prompt('أدخل الاسم الجديد للموقع:', site.name || site.title || site.url);
    const newUrl = prompt('أدخل الرابط الجديد للموقع:', site.url || site.href);

    if (newName && newUrl) {
        site.name = newName.trim();
        site.url = newUrl.trim();
        site.href = newUrl.trim(); // للتوافق مع الإصدارات القديمة

        saveAppData();
        updateCategoriesCounts();
        renderCategories();
        displayManagedMovies();

        // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
        if (appState.currentCategory === 'movie-sites') {
            displayMovies('movie-sites', appState.currentPage);
        }

        showToast('تم تحديث الموقع بنجاح', 'success');
    }
}

function deleteSiteFromManagement(siteIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat || !movieSitesCat.shortcuts || siteIndex >= movieSitesCat.shortcuts.length) return;

    const site = movieSitesCat.shortcuts[siteIndex];
    movieSitesCat.shortcuts.splice(siteIndex, 1);

    saveAppData();
    updateCategoriesCounts();
    renderCategories();
    displayManagedMovies();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast(`تم حذف الموقع "${site.name || site.url}" بنجاح`, 'success');
}

// دوال ترتيب المواقع داخل المجلدات
function moveSiteInFolder(folderId, fromIndex, toIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder || !folder.sites) return;

    // التحقق من صحة المؤشرات
    if (fromIndex < 0 || fromIndex >= folder.sites.length ||
        toIndex < 0 || toIndex >= folder.sites.length ||
        fromIndex === toIndex) return;

    // نقل الموقع
    const site = folder.sites.splice(fromIndex, 1)[0];
    folder.sites.splice(toIndex, 0, site);

    saveAppData();
    updateCategoriesCounts();
    renderCategories();

    // تحديث العرض إذا كان المستخدم في قسم مواقع الأفلام
    if (appState.currentCategory === 'movie-sites') {
        displayMovies('movie-sites', appState.currentPage);
    }

    showToast('تم تغيير ترتيب الموقع بنجاح', 'success');
}

function changeSitePosition(folderId, currentIndex) {
    const movieSitesCat = appState.categories.main.find(cat => cat.id === 'movie-sites');
    if (!movieSitesCat) return;

    const folder = movieSitesCat.folders.find(f => f.id === folderId);
    if (!folder || !folder.sites || folder.sites.length <= 1) return;

    const site = folder.sites[currentIndex];
    const newPosition = prompt(
        `أدخل الموضع الجديد للموقع "${site.name || site.url}"\n` +
        `الموضع الحالي: ${currentIndex + 1}\n` +
        `المواضع المتاحة: 1 إلى ${folder.sites.length}`,
        (currentIndex + 1).toString()
    );

    if (!newPosition) return;

    const newIndex = parseInt(newPosition) - 1;

    if (isNaN(newIndex) || newIndex < 0 || newIndex >= folder.sites.length) {
        showToast('رقم الموضع غير صحيح', 'warning');
        return;
    }

    if (newIndex === currentIndex) {
        showToast('الموقع في نفس الموضع بالفعل', 'info');
        return;
    }

    // نقل الموقع إلى الموضع الجديد
    moveSiteInFolder(folderId, currentIndex, newIndex);
}

// إعداد نظام الاختيار بالأرقام للقوائم المنسدلة
function setupNumberInputs() {
    console.log('Setting up number inputs...');

    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        // خريطة الخيارات للقوائم المختلفة
        const selectMappings = {
            'sort-options': {
                1: 'name',
                2: 'site',
                3: 'date',
                4: 'date-asc',
                5: 'star'
            },
            'view-mode': {
                1: 'grid',
                2: 'list'
            }
        };

        // إعداد حقول الإدخال الرقمية مع أزرار التنفيذ
        setupNumberInputWithButton('sort-options-input', 'sort-options-apply', 'sort-options', selectMappings['sort-options'], (value) => {
            console.log('Sort callback called with:', value);
            appState.sortBy = value;
            updateFilterVisibility();
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            showToast(`تم تغيير الترتيب إلى: ${getOptionText('sort-options', value)}`, 'success');
        });

        setupNumberInputWithButton('view-mode-input', 'view-mode-apply', 'view-mode', selectMappings['view-mode'], (value) => {
            console.log('View mode callback called with:', value);
            appState.viewMode = value;
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            showToast(`تم تغيير العرض إلى: ${getOptionText('view-mode', value)}`, 'success');
        });

        // إعداد حقول الفلاتر الديناميكية مع أزرار التنفيذ
        setupDynamicNumberInputWithButton('site-filter-input', 'site-filter-apply', 'site-filter', (value) => {
            console.log('Site filter callback called with:', value);
            appState.selectedSite = value;
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            const siteName = value || 'جميع المواقع';
            showToast(`تم تطبيق فلتر الموقع: ${siteName}`, 'success');
        });

        setupDynamicNumberInputWithButton('star-filter-input', 'star-filter-apply', 'star-filter', (value) => {
            console.log('Star filter callback called with:', value);
            appState.selectedStar = value;
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            const starName = value || 'جميع النجوم';
            showToast(`تم تطبيق فلتر النجم: ${starName}`, 'success');
        });

        // تحديث الأرقام في خيارات الفلاتر عند تغييرها
        const originalPopulateSiteFilterMain = populateSiteFilterMain;
        populateSiteFilterMain = function() {
            originalPopulateSiteFilterMain.call(this);
            updateDynamicSelectNumbers('site-filter');
            updateNumberInputFromSelect('site-filter-input', 'site-filter');
        };

        const originalPopulateStarFilter = populateStarFilter;
        populateStarFilter = function() {
            originalPopulateStarFilter.call(this);
            updateDynamicSelectNumbers('star-filter');
            updateNumberInputFromSelect('star-filter-input', 'star-filter');
        };

        // تحديث حقول الإدخال الرقمية بالقيم الحالية
        updateNumberInputsFromSelects();

        console.log('Number inputs setup completed');

        // إضافة مستمعي أحداث مباشرة كبديل
        setupDirectEventListeners();
    }, 500);
}

// إعداد مستمعي أحداث مباشرة للأزرار
function setupDirectEventListeners() {
    console.log('Setting up direct event listeners...');

    // زر ترتيب الأفلام
    const sortApplyBtn = document.getElementById('sort-options-apply');
    if (sortApplyBtn) {
        sortApplyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Sort apply button clicked directly');

            const input = document.getElementById('sort-options-input');
            const select = document.getElementById('sort-options');

            if (input && select) {
                const number = parseInt(input.value);
                const mapping = { 1: 'name', 2: 'site', 3: 'date', 4: 'date-asc', 5: 'star' };

                if (number && mapping[number]) {
                    console.log(`Setting sort to: ${mapping[number]}`);
                    select.value = mapping[number];
                    appState.sortBy = mapping[number];
                    updateFilterVisibility();
                    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                    saveAppData();
                    showToast(`تم تغيير الترتيب إلى: ${getOptionText('sort-options', mapping[number])}`, 'success');
                }
            }
        });
    }

    // زر نوع العرض
    const viewApplyBtn = document.getElementById('view-mode-apply');
    if (viewApplyBtn) {
        viewApplyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('View mode apply button clicked directly');

            const input = document.getElementById('view-mode-input');
            const select = document.getElementById('view-mode');

            if (input && select) {
                const number = parseInt(input.value);
                const mapping = { 1: 'grid', 2: 'list' };

                if (number && mapping[number]) {
                    console.log(`Setting view mode to: ${mapping[number]}`);
                    select.value = mapping[number];
                    appState.viewMode = mapping[number];
                    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                    saveAppData();
                    showToast(`تم تغيير العرض إلى: ${getOptionText('view-mode', mapping[number])}`, 'success');
                }
            }
        });
    }

    // زر فلتر المواقع
    const siteApplyBtn = document.getElementById('site-filter-apply');
    if (siteApplyBtn) {
        siteApplyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Site filter apply button clicked directly');

            const input = document.getElementById('site-filter-input');
            const select = document.getElementById('site-filter');

            if (input && select) {
                const number = parseInt(input.value);

                if (number === 0) {
                    select.value = '';
                    appState.selectedSite = '';
                    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                    saveAppData();
                    showToast('تم تطبيق فلتر الموقع: جميع المواقع', 'success');
                } else if (number > 0 && number <= select.options.length - 1) {
                    const option = select.options[number];
                    if (option) {
                        select.value = option.value;
                        appState.selectedSite = option.value;
                        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                        saveAppData();
                        showToast(`تم تطبيق فلتر الموقع: ${option.value}`, 'success');
                    }
                }
            }
        });
    }

    // زر فلتر النجوم
    const starApplyBtn = document.getElementById('star-filter-apply');
    if (starApplyBtn) {
        starApplyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Star filter apply button clicked directly');

            const input = document.getElementById('star-filter-input');
            const select = document.getElementById('star-filter');

            if (input && select) {
                const number = parseInt(input.value);

                if (number === 0) {
                    select.value = '';
                    appState.selectedStar = '';
                    displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                    saveAppData();
                    showToast('تم تطبيق فلتر النجم: جميع النجوم', 'success');
                } else if (number > 0 && number <= select.options.length - 1) {
                    const option = select.options[number];
                    if (option) {
                        select.value = option.value;
                        appState.selectedStar = option.value;
                        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
                        saveAppData();
                        showToast(`تم تطبيق فلتر النجم: ${option.value}`, 'success');
                    }
                }
            }
        });
    }

    console.log('Direct event listeners setup completed');
}

// وظائف التطبيق المباشرة للأزرار (تستدعى من HTML)
function applySortOption() {
    console.log('applySortOption called');

    const input = document.getElementById('sort-options-input');
    const select = document.getElementById('sort-options');

    if (!input || !select) {
        console.error('Sort elements not found');
        return;
    }

    const number = parseInt(input.value);
    const mapping = { 1: 'name', 2: 'site', 3: 'date', 4: 'date-asc', 5: 'star' };

    console.log(`Sort number: ${number}, mapping:`, mapping);

    if (number && mapping[number]) {
        console.log(`Setting sort to: ${mapping[number]}`);
        select.value = mapping[number];
        appState.sortBy = mapping[number];
        updateFilterVisibility();
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
        saveAppData();
        showToast(`تم تغيير الترتيب إلى: ${getOptionText('sort-options', mapping[number])}`, 'success');
    } else {
        showToast('رقم الترتيب غير صحيح. استخدم رقم من 1 إلى 5', 'warning');
    }
}

function applyViewMode() {
    console.log('applyViewMode called');

    const input = document.getElementById('view-mode-input');
    const select = document.getElementById('view-mode');

    if (!input || !select) {
        console.error('View mode elements not found');
        return;
    }

    const number = parseInt(input.value);
    const mapping = { 1: 'grid', 2: 'list' };

    console.log(`View mode number: ${number}, mapping:`, mapping);

    if (number && mapping[number]) {
        console.log(`Setting view mode to: ${mapping[number]}`);
        select.value = mapping[number];
        appState.viewMode = mapping[number];
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
        saveAppData();
        showToast(`تم تغيير العرض إلى: ${getOptionText('view-mode', mapping[number])}`, 'success');
    } else {
        showToast('رقم العرض غير صحيح. استخدم 1 للشبكي أو 2 للقائمة', 'warning');
    }
}

function applySiteFilter() {
    console.log('applySiteFilter called');

    const input = document.getElementById('site-filter-input');
    const select = document.getElementById('site-filter');

    if (!input || !select) {
        console.error('Site filter elements not found');
        return;
    }

    const number = parseInt(input.value);

    console.log(`Site filter number: ${number}, options length: ${select.options.length}`);

    if (number === 0) {
        select.value = '';
        appState.selectedSite = '';
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
        saveAppData();
        showToast('تم تطبيق فلتر الموقع: جميع المواقع', 'success');
    } else if (number > 0 && number <= select.options.length - 1) {
        const option = select.options[number];
        if (option) {
            select.value = option.value;
            appState.selectedSite = option.value;
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            showToast(`تم تطبيق فلتر الموقع: ${option.value}`, 'success');
        }
    } else {
        showToast(`رقم الموقع غير صحيح. استخدم 0 للكل أو رقم من 1 إلى ${select.options.length - 1}`, 'warning');
    }
}

function applyStarFilter() {
    console.log('applyStarFilter called');

    const input = document.getElementById('star-filter-input');
    const select = document.getElementById('star-filter');

    if (!input || !select) {
        console.error('Star filter elements not found');
        return;
    }

    const number = parseInt(input.value);

    console.log(`Star filter number: ${number}, options length: ${select.options.length}`);

    if (number === 0) {
        select.value = '';
        appState.selectedStar = '';
        displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
        saveAppData();
        showToast('تم تطبيق فلتر النجم: جميع النجوم', 'success');
    } else if (number > 0 && number <= select.options.length - 1) {
        const option = select.options[number];
        if (option) {
            select.value = option.value;
            appState.selectedStar = option.value;
            displayMoviesWithoutScroll(appState.currentCategory, appState.currentPage);
            saveAppData();
            showToast(`تم تطبيق فلتر النجم: ${option.value}`, 'success');
        }
    } else {
        showToast(`رقم النجم غير صحيح. استخدم 0 للكل أو رقم من 1 إلى ${select.options.length - 1}`, 'warning');
    }
}

// إعداد حقل إدخال رقمي مع زر تنفيذ لقائمة منسدلة ثابتة
function setupNumberInputWithButton(inputId, buttonId, selectId, mapping, callback) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const select = document.getElementById(selectId);

    console.log(`Setting up number input: ${inputId}, button: ${buttonId}, select: ${selectId}`);
    console.log('Elements found:', { input: !!input, button: !!button, select: !!select });

    if (!input || !button || !select) {
        console.error(`Missing elements for ${inputId}:`, { input: !!input, button: !!button, select: !!select });
        return;
    }

    // وظيفة تطبيق الاختيار
    const applySelection = () => {
        const number = parseInt(input.value);
        console.log(`Applying selection: number=${number}, mapping=`, mapping);

        if (number && mapping[number]) {
            console.log(`Setting select value to: ${mapping[number]}`);
            select.value = mapping[number];
            if (callback) {
                console.log('Calling callback with:', mapping[number]);
                callback(mapping[number]);
            }
        } else {
            console.log('Invalid number or mapping not found');
        }
    };

    // مستمع زر التنفيذ
    button.addEventListener('click', (e) => {
        console.log(`Button clicked: ${buttonId}`);
        e.preventDefault();
        applySelection();
    });

    // مستمع Enter في حقل الإدخال
    input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            applySelection();
        }
    });

    // مستمع تغيير فوري (تحديث القائمة المنسدلة فقط)
    input.addEventListener('input', (e) => {
        const number = parseInt(e.target.value);
        if (number && mapping[number]) {
            // تحديث القائمة المنسدلة فقط بدون تنفيذ الكولباك
            select.value = mapping[number];
        }
    });

    // مستمع تغيير القائمة المنسدلة لتحديث حقل الإدخال (بدون تنفيذ الكولباك لتجنب التضارب)
    select.addEventListener('change', (e) => {
        const value = e.target.value;
        const number = Object.keys(mapping).find(key => mapping[key] === value);
        if (number) {
            input.value = number;
        }
        // لا نستدعي الكولباك هنا لتجنب التضارب مع مستمعي الأحداث الأخرى
    });

    // تحديث حقل الإدخال بالقيمة الحالية
    const currentValue = select.value;
    const currentNumber = Object.keys(mapping).find(key => mapping[key] === currentValue);
    if (currentNumber) {
        input.value = currentNumber;
    }
}

// إعداد حقل إدخال رقمي مع زر تنفيذ لقائمة منسدلة ديناميكية
function setupDynamicNumberInputWithButton(inputId, buttonId, selectId, callback) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const select = document.getElementById(selectId);

    console.log(`Setting up dynamic number input: ${inputId}, button: ${buttonId}, select: ${selectId}`);
    console.log('Elements found:', { input: !!input, button: !!button, select: !!select });

    if (!input || !button || !select) {
        console.error(`Missing elements for ${inputId}:`, { input: !!input, button: !!button, select: !!select });
        return;
    }

    // وظيفة تطبيق الاختيار
    const applySelection = () => {
        const number = parseInt(input.value);
        console.log(`Applying dynamic selection: number=${number}, options length=${select.options.length}`);

        if (number === 0) {
            console.log('Setting select to empty (all)');
            select.value = '';
            if (callback) {
                console.log('Calling callback with empty value');
                callback('');
            }
        } else if (number > 0 && number <= select.options.length - 1) {
            const option = select.options[number];
            if (option) {
                console.log(`Setting select value to: ${option.value}`);
                select.value = option.value;
                if (callback) {
                    console.log('Calling callback with:', option.value);
                    callback(option.value);
                }
            }
        } else {
            console.log('Invalid number or out of range');
        }
    };

    // مستمع زر التنفيذ
    button.addEventListener('click', (e) => {
        console.log(`Dynamic button clicked: ${buttonId}`);
        e.preventDefault();
        applySelection();
    });

    // مستمع Enter في حقل الإدخال
    input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            applySelection();
        }
    });

    // مستمع تغيير فوري (تحديث القائمة المنسدلة فقط)
    input.addEventListener('input', (e) => {
        const number = parseInt(e.target.value);
        if (number === 0) {
            // تحديث القائمة المنسدلة فقط بدون تنفيذ الكولباك
            select.value = '';
        } else if (number > 0 && number <= select.options.length - 1) {
            const option = select.options[number];
            if (option) {
                select.value = option.value;
            }
        }
    });

    // مستمع تغيير القائمة المنسدلة لتحديث حقل الإدخال (بدون تنفيذ الكولباك لتجنب التضارب)
    select.addEventListener('change', (e) => {
        const value = e.target.value;
        if (value === '') {
            input.value = '0';
        } else {
            const optionIndex = Array.from(select.options).findIndex(opt => opt.value === value);
            if (optionIndex > 0) {
                input.value = optionIndex.toString();
            }
        }
        // لا نستدعي الكولباك هنا لتجنب التضارب مع مستمعي الأحداث الأخرى
    });
}

// الحصول على نص الخيار من القائمة المنسدلة
function getOptionText(selectId, value) {
    const select = document.getElementById(selectId);
    if (!select) return value;

    const option = Array.from(select.options).find(opt => opt.value === value);
    return option ? option.textContent.replace(/^\d+\.\s*/, '') : value;
}

// تحديث حقل الإدخال الرقمي من القائمة المنسدلة
function updateNumberInputFromSelect(inputId, selectId) {
    const input = document.getElementById(inputId);
    const select = document.getElementById(selectId);

    if (!input || !select) return;

    const value = select.value;
    if (value === '') {
        input.value = '0';
    } else {
        const optionIndex = Array.from(select.options).findIndex(opt => opt.value === value);
        if (optionIndex > 0) {
            input.value = optionIndex.toString();
        }
    }
}

// تحديث جميع حقول الإدخال الرقمية من القوائم المنسدلة
function updateNumberInputsFromSelects() {
    // تحديث حقول الترتيب الثابتة
    const sortMappings = {
        'name': '1',
        'site': '2',
        'date': '3',
        'date-asc': '4',
        'star': '5'
    };

    const viewMappings = {
        'grid': '1',
        'list': '2'
    };

    // تحديث حقل الترتيب
    const sortSelect = document.getElementById('sort-options');
    const sortInput = document.getElementById('sort-options-input');
    if (sortSelect && sortInput) {
        const sortValue = sortSelect.value;
        if (sortMappings[sortValue]) {
            sortInput.value = sortMappings[sortValue];
        }
    }

    // تحديث حقل العرض
    const viewSelect = document.getElementById('view-mode');
    const viewInput = document.getElementById('view-mode-input');
    if (viewSelect && viewInput) {
        const viewValue = viewSelect.value;
        if (viewMappings[viewValue]) {
            viewInput.value = viewMappings[viewValue];
        }
    }

    // تحديث حقول الفلاتر الديناميكية
    updateNumberInputFromSelect('site-filter-input', 'site-filter');
    updateNumberInputFromSelect('star-filter-input', 'star-filter');
}

// إعداد حقل إدخال رقمي لقائمة منسدلة ديناميكية
function setupDynamicNumberInput(inputId, selectId, callback) {
    const input = document.getElementById(inputId);
    const select = document.getElementById(selectId);

    if (!input || !select) return;

    // مستمع تغيير حقل الإدخال
    input.addEventListener('input', (e) => {
        const number = parseInt(e.target.value);
        if (number === 0) {
            select.value = '';
            if (callback) callback('');
        } else if (number > 0 && number <= select.options.length - 1) {
            const option = select.options[number];
            if (option) {
                select.value = option.value;
                if (callback) callback(option.value);
            }
        }
    });

    // مستمع تغيير القائمة المنسدلة لتحديث حقل الإدخال
    select.addEventListener('change', (e) => {
        const value = e.target.value;
        if (value === '') {
            input.value = '0';
        } else {
            const optionIndex = Array.from(select.options).findIndex(opt => opt.value === value);
            if (optionIndex > 0) {
                input.value = optionIndex.toString();
            }
        }
        if (callback) callback(value);
    });
}

// تحديث أرقام خيارات القائمة المنسدلة الديناميكية
function updateDynamicSelectNumbers(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // إضافة أرقام للخيارات
    Array.from(select.options).forEach((option, index) => {
        if (index === 0) {
            // الخيار الأول (الكل)
            if (!option.textContent.startsWith('0.')) {
                option.textContent = '0. ' + option.textContent;
            }
        } else {
            // باقي الخيارات
            if (!option.textContent.startsWith(index + '.')) {
                option.textContent = index + '. ' + option.textContent;
            }
        }
    });
}

// تهيئة التطبيق
function initializeApp() {
    loadAppData().then(() => {
        renderCategories();
        displayMovies('all', 1);
        setupEventListeners();
        setupKeyboardShortcuts();
        setupZoomControls();
        setupPasswordProtection();
        setupMovieOpenMode();
        setupSitesManagement();
        setupCategorySortingTool();
        setupSubcategoryManagement();
        setupBulkOperations();
        setupMovieManagement();
        setupDataManagement();
        setupAdvancedSearch();
        setupMovieFilters();
        setupCategoryManagement();
        setupSitesBulkActions();
        setupNumberInputs(); // إضافة نظام الاختيار بالأرقام
        updateCategoriesCounts();

        // تحديث عنوان الصفحة الأولي
        const initialTitle = 'جميع الأفلام والمسلسلات - New Koktil-aflam v25';
        document.title = initialTitle;
        appState.currentPageTitle = initialTitle;

        console.log('تم تحميل التطبيق بنجاح');
    }).catch(error => {
        console.error('خطأ في تحميل التطبيق:', error);
        showToast('خطأ في تحميل البيانات', 'error');
    });
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);